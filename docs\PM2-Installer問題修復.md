# PM2-Installer 命令不可用問題修復

## 🐛 問題描述

在執行 Windows 系統啟動配置時出現以下錯誤：
```
✗ 配置 Windows 啟動項失敗: 無法辨識 'pm2-installer' 詞彙是否為 Cmdlet、函數、指令檔或可執行程式的名稱。
```

## 🔍 問題分析

### 根本原因
1. **PATH 環境變數問題**：npm 全域安裝的 pm2-installer 沒有正確添加到 PATH
2. **PowerShell 會話問題**：新安裝的命令需要重新啟動 PowerShell 會話才能使用
3. **Windows 權限問題**：某些情況下全域安裝的命令需要特殊權限才能執行

### 常見情況
- npm 全域安裝成功，但命令不可用
- 環境變數更新延遲
- Windows 系統的命令註冊延遲

## ✅ 解決方案

### 方案 1：使用修復腳本（推薦）
執行專門的修復腳本：
```powershell
.\Fix-PM2Installer.ps1
```

### 方案 2：手動修復步驟

#### 步驟 1：檢查安裝狀態
```powershell
# 檢查 pm2-installer 是否已安裝
npm list -g pm2-installer

# 檢查命令是否可用
Get-Command pm2-installer -ErrorAction SilentlyContinue
```

#### 步驟 2：刷新環境變數
```powershell
# 刷新當前會話的環境變數
$env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
```

#### 步驟 3：使用替代方案
如果命令仍不可用，使用 npx：
```powershell
# 保存 PM2 配置
pm2 save

# 使用 npx 安裝服務
npx pm2-installer install

# 配置服務
Set-Service -Name "PM2" -StartupType Automatic
Start-Service -Name "PM2"
```

### 方案 3：重新安裝
```powershell
# 卸載並重新安裝
npm uninstall -g pm2-installer
npm install -g pm2-installer

# 重新啟動 PowerShell 會話
```

## 🔧 腳本修復

### 修復後的 Configure-WindowsStartup 函數
```powershell
function Configure-WindowsStartup {
    try {
        # 保存 PM2 配置
        pm2 save 2>$null | Out-Null
        
        # 刷新環境變數
        Update-EnvironmentVariables
        
        # 檢查命令是否可用
        $pm2InstallerPath = Get-Command "pm2-installer" -ErrorAction SilentlyContinue
        if (-not $pm2InstallerPath) {
            # 使用 npx 作為備用方案
            Write-Info "使用 npx 執行 pm2-installer..."
            npx pm2-installer install 2>$null | Out-Null
        } else {
            # 使用直接命令
            pm2-installer install 2>$null | Out-Null
        }
        
        # 等待服務安裝完成
        Start-Sleep -Seconds 8
        
        # 檢查並配置服務
        $service = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if ($service) {
            Set-Service -Name "PM2" -StartupType Automatic
            if ($service.Status -ne "Running") {
                Start-Service -Name "PM2"
            }
            return $true
        }
        
        return $false
    }
    catch {
        # 備用方案：使用 npx
        try {
            npx pm2-installer install 2>$null | Out-Null
            Start-Sleep -Seconds 8
            
            $retryService = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
            if ($retryService) {
                Set-Service -Name "PM2" -StartupType Automatic
                if ($retryService.Status -ne "Running") {
                    Start-Service -Name "PM2"
                }
                return $true
            }
        }
        catch {
            return $false
        }
    }
}
```

## 🧪 驗證修復

### 檢查服務狀態
```powershell
# 檢查 Windows 服務
Get-Service -Name "PM2"

# 檢查 PM2 進程
pm2 list

# 檢查 Uptime Kuma 進程
pm2 list | Select-String "uptime-kuma"
```

### 測試自動啟動
1. 重新啟動電腦
2. 等待系統完全啟動
3. 檢查服務是否自動運行：
   ```powershell
   Get-Service -Name "PM2"
   pm2 list
   ```
4. 訪問 http://localhost:3001

## 📝 預防措施

### 安裝時的最佳實踐
1. **以管理員身份運行**：確保有足夠權限
2. **等待安裝完成**：不要在安裝過程中中斷
3. **刷新環境變數**：安裝後刷新 PATH
4. **驗證安裝**：檢查命令是否可用

### 環境配置
```powershell
# 檢查 npm 配置
npm config get prefix

# 確保 npm 全域路徑在 PATH 中
$npmPrefix = npm config get prefix
if ($env:PATH -notmatch [regex]::Escape($npmPrefix)) {
    Write-Warning "npm 全域路徑不在 PATH 中"
}
```

## 🔄 故障排除流程

1. **檢查安裝**：確認 pm2-installer 已正確安裝
2. **刷新環境**：更新 PATH 環境變數
3. **使用 npx**：如果直接命令不可用，使用 npx
4. **檢查權限**：確保以管理員身份運行
5. **重新安裝**：如果以上都失敗，重新安裝 pm2-installer

## 🎯 最終解決方案

修復後的腳本現在包含：
- ✅ 自動環境變數刷新
- ✅ 命令可用性檢查
- ✅ npx 備用方案
- ✅ 多重錯誤處理
- ✅ 延長等待時間
- ✅ 服務狀態驗證

這確保了即使在各種環境問題下，Windows 系統啟動配置也能成功完成。
