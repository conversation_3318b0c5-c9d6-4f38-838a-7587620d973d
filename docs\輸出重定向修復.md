# PowerShell 輸出重定向錯誤修復

## 🐛 問題描述

執行腳本時出現以下錯誤：
```
✗ 啟動 Uptime Kuma 服務失敗: 找不到磁碟機。名為 ' added 692 packages, and audited 693 packages in 2m  103 packages are looking for funding   run `npm fund` for details  8 vulnerabilities (3 low, 5 high)  To address issues that do not require attention, run' 的磁碟機不存在。
```

## 🔍 問題分析

這個錯誤是由於 PowerShell 錯誤地將 npm/choco 等命令的輸出信息解析為磁碟機名稱造成的。

### 根本原因
1. **輸出重定向問題**：使用 `2>$null` 只重定向錯誤流，但 npm 的狀態信息通過標準輸出流輸出
2. **PowerShell 解析錯誤**：PowerShell 將多行輸出錯誤地解析為磁碟機路徑
3. **錯誤處理不當**：沒有正確處理命令的所有輸出流

## ✅ 修復方案

使用 `*>$null` 重定向所有輸出流，並添加更好的錯誤處理。

## 🔧 具體修復

### 1. 修復 Start-UptimeKumaService 函數
```powershell
# 修復前
pm2 delete uptime-kuma 2>$null | Out-Null
pm2 start server/server.js --name uptime-kuma
pm2 save

# 修復後
try {
    pm2 delete uptime-kuma *>$null
}
catch {
    # 忽略刪除不存在進程的錯誤
}

pm2 start server/server.js --name uptime-kuma *>$null
pm2 save *>$null
```

### 2. 修復 Install-UptimeKuma 函數
```powershell
# 修復前
git clone https://github.com/louislam/uptime-kuma.git .
git checkout 2.0.0-beta.2
npm ci --production
npm run download-dist

# 修復後
Write-Info "正在下載 Uptime Kuma 源碼..."
git clone https://github.com/louislam/uptime-kuma.git . *>$null
git checkout 2.0.0-beta.2 *>$null

Write-Info "正在安裝 Node.js 依賴（這可能需要幾分鐘）..."
npm ci --production --silent *>$null
Write-Info "正在下載預編譯資源..."
npm run download-dist --silent *>$null
```

### 3. 修復其他安裝函數
```powershell
# Git 安裝
Write-Info "正在通過 Chocolatey 安裝 Git..."
choco install git -y *>$null

# Node.js 安裝
Write-Info "正在通過 Chocolatey 安裝 Node.js..."
choco install nodejs -y *>$null

# PM2 安裝
Write-Info "正在全域安裝 PM2..."
npm install -g pm2 --silent *>$null

# PM2 插件安裝
pm2 install pm2-logrotate *>$null
```

## 📝 PowerShell 輸出重定向說明

### 輸出流類型
- `1>` - 標準輸出 (stdout)
- `2>` - 錯誤輸出 (stderr)
- `3>` - 警告輸出
- `4>` - 詳細輸出
- `5>` - 調試輸出
- `6>` - 信息輸出
- `*>` - 所有輸出流

### 最佳實踐
```powershell
# ❌ 錯誤：只重定向錯誤流
command 2>$null

# ✅ 正確：重定向所有輸出流
command *>$null

# ✅ 也可以：分別重定向
command >$null 2>&1
```

## 🎯 修復效果

1. **消除錯誤信息**：不再出現磁碟機不存在的錯誤
2. **清潔輸出**：安裝過程中只顯示重要的進度信息
3. **更好的用戶體驗**：用戶看到的是清晰的安裝步驟，而不是大量的技術輸出
4. **穩定性提升**：避免了 PowerShell 解析錯誤導致的異常

## 🧪 測試建議

修復後建議測試：
1. 完整的安裝流程
2. 重複安裝（測試跳過邏輯）
3. 網路中斷情況下的錯誤處理
4. PM2 服務的啟動和狀態檢查

這樣的修復確保了腳本的穩定性和用戶友好性。
