# 測試 SYSTEM 帳戶下的 PM2 檢測功能

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

# 顯示當前用戶信息
function Show-CurrentUser {
    Write-Info "當前用戶信息："
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    Write-ColorOutput "  用戶名: $($identity.Name)" "Gray"
    Write-ColorOutput "  認證類型: $($identity.AuthenticationType)" "Gray"
    Write-ColorOutput "  是否為系統帳戶: $($identity.IsSystem)" "Gray"
    Write-ColorOutput "  是否為匿名: $($identity.IsAnonymous)" "Gray"
}

# 顯示環境變數
function Show-EnvironmentInfo {
    Write-Info "環境變數信息："
    Write-ColorOutput "  PATH: $env:PATH" "Gray"
    Write-ColorOutput "  APPDATA: $env:APPDATA" "Gray"
    Write-ColorOutput "  ProgramFiles: $env:ProgramFiles" "Gray"
    Write-ColorOutput "  USERPROFILE: $env:USERPROFILE" "Gray"
}

# 尋找 Node.js 安裝路徑（複製自啟動腳本）
function Find-NodePath {
    Write-Info "尋找 Node.js 安裝路徑..."
    
    $possibleNodePaths = @(
        "${env:ProgramFiles}\nodejs\node.exe",
        "${env:ProgramFiles(x86)}\nodejs\node.exe",
        "C:\Program Files\nodejs\node.exe",
        "C:\Program Files (x86)\nodejs\node.exe"
    )
    
    foreach ($path in $possibleNodePaths) {
        Write-ColorOutput "  檢查: $path" "Gray"
        if (Test-Path $path) {
            $nodeDir = Split-Path $path -Parent
            Write-Success "找到 Node.js: $nodeDir"
            return $nodeDir
        }
    }
    
    # 嘗試在 PATH 中尋找
    try {
        $nodeInPath = Get-Command node -ErrorAction SilentlyContinue
        if ($nodeInPath) {
            $nodeDir = Split-Path $nodeInPath.Source -Parent
            Write-Success "在 PATH 中找到 Node.js: $nodeDir"
            return $nodeDir
        }
    }
    catch {
        # 忽略錯誤
    }
    
    Write-Error "未找到 Node.js"
    return $null
}

# 尋找 PM2 執行檔（複製自啟動腳本）
function Find-PM2Path {
    Write-Info "尋找 PM2 執行檔..."
    
    # 首先尋找 Node.js 路徑
    $nodeDir = Find-NodePath
    
    # 可能的 PM2 路徑
    $possiblePaths = @()
    
    if ($nodeDir) {
        $possiblePaths += @(
            "$nodeDir\pm2.cmd",
            "$nodeDir\pm2",
            "$nodeDir\node_modules\.bin\pm2.cmd",
            "$nodeDir\node_modules\.bin\pm2"
        )
    }
    
    # 添加其他常見路徑
    $possiblePaths += @(
        "${env:APPDATA}\npm\pm2.cmd",
        "${env:APPDATA}\npm\pm2",
        "C:\Users\<USER>\AppData\Roaming\npm\pm2.cmd",
        "C:\Users\<USER>\AppData\Roaming\npm\pm2",
        "${env:ProgramFiles}\nodejs\pm2.cmd",
        "${env:ProgramFiles}\nodejs\pm2"
    )
    
    foreach ($path in $possiblePaths) {
        Write-ColorOutput "  檢查: $path" "Gray"
        if (Test-Path $path) {
            Write-Success "找到 PM2: $path"
            return $path
        }
    }
    
    # 嘗試在 PATH 中尋找
    try {
        $pm2InPath = Get-Command pm2 -ErrorAction SilentlyContinue
        if ($pm2InPath) {
            Write-Success "在 PATH 中找到 PM2: $($pm2InPath.Source)"
            return $pm2InPath.Source
        }
    }
    catch {
        # 忽略錯誤
    }
    
    # 最後嘗試：使用 Node.js 直接執行 PM2
    if ($nodeDir) {
        $pm2JSPath = "$nodeDir\node_modules\pm2\bin\pm2"
        Write-ColorOutput "  檢查: $pm2JSPath" "Gray"
        if (Test-Path $pm2JSPath) {
            Write-Success "找到 PM2 JavaScript 文件: $pm2JSPath"
            return "$nodeDir\node.exe `"$pm2JSPath`""
        }
    }
    
    Write-Error "未找到 PM2 執行檔"
    return $null
}

# 測試 PM2 執行
function Test-PM2Execution {
    param([string]$PM2Path)
    
    Write-Info "測試 PM2 執行..."
    
    if (-not $PM2Path) {
        Write-Error "PM2 路徑為空"
        return $false
    }
    
    try {
        # 創建執行函數
        $pm2Command = if ($PM2Path -like "*node.exe*") {
            # 如果是 Node.js 執行方式
            { param($args) Invoke-Expression "$PM2Path $args" }
        } else {
            # 如果是直接執行檔
            { param($args) & $PM2Path $args }
        }
        
        # 測試版本命令
        Write-Info "執行 PM2 版本檢查..."
        $pm2Version = & $pm2Command "--version" 2>$null
        
        if ($pm2Version) {
            Write-Success "PM2 版本: $pm2Version"
            
            # 測試列表命令
            Write-Info "執行 PM2 列表命令..."
            $pm2List = & $pm2Command "list" 2>$null
            Write-Success "PM2 列表命令執行成功"
            Write-ColorOutput "PM2 輸出: $pm2List" "Gray"
            
            return $true
        } else {
            Write-Error "PM2 版本檢查失敗"
            return $false
        }
    }
    catch {
        Write-Error "PM2 執行測試失敗: $($_.Exception.Message)"
        return $false
    }
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== SYSTEM 帳戶 PM2 檢測測試 ===" "Magenta"
        
        # 顯示當前用戶信息
        Show-CurrentUser
        
        Write-ColorOutput "`n" "White"
        
        # 顯示環境變數信息
        Show-EnvironmentInfo
        
        Write-ColorOutput "`n" "White"
        
        # 測試 PM2 檢測
        $pm2Path = Find-PM2Path
        
        Write-ColorOutput "`n" "White"
        
        # 測試 PM2 執行
        if ($pm2Path) {
            $pm2Works = Test-PM2Execution -PM2Path $pm2Path
            
            Write-ColorOutput "`n=== 測試結果 ===" "Magenta"
            if ($pm2Works) {
                Write-Success "PM2 檢測和執行測試通過！"
                Write-Info "啟動腳本應該能夠在 SYSTEM 帳戶下正常工作"
            } else {
                Write-Error "PM2 執行測試失敗"
                Write-Info "需要檢查 PM2 安裝或路徑配置"
            }
        } else {
            Write-ColorOutput "`n=== 測試結果 ===" "Magenta"
            Write-Error "PM2 檢測失敗"
            Write-Info "請確認 PM2 已正確安裝"
        }
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "測試過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
