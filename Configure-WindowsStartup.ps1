#Requires -Version 5.1

<#
.SYNOPSIS
    配置 Uptime Kuma Windows 系統啟動項
.DESCRIPTION
    將 PM2 和 Uptime Kuma 配置為 Windows 服務，實現系統重啟後自動運行
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Ask-UserPreference {
    param([string]$Question, [string]$DefaultChoice = "Y")
    
    Write-ColorOutput "`n$Question" "Yellow"
    Write-ColorOutput "請輸入 Y (是) 或 N (否)，預設為 $DefaultChoice" "Gray"
    
    do {
        $choice = Read-Host "您的選擇"
        if ([string]::IsNullOrWhiteSpace($choice)) {
            $choice = $DefaultChoice
        }
        $choice = $choice.ToUpper()
    } while ($choice -ne "Y" -and $choice -ne "N")
    
    return $choice -eq "Y"
}

function Configure-WindowsStartup {
    Write-Info "配置 Windows 系統啟動項..."
    
    try {
        # 檢查是否已經安裝為服務
        $serviceStatus = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if ($serviceStatus) {
            Write-Warning "PM2 服務已存在"
            
            # 檢查服務狀態
            if ($serviceStatus.Status -eq "Running") {
                Write-Success "PM2 服務正在運行中"
            } else {
                Write-Info "正在啟動 PM2 服務..."
                Start-Service -Name "PM2"
                Write-Success "PM2 服務已啟動"
            }
            
            # 確保服務為自動啟動
            if ($serviceStatus.StartType -ne "Automatic") {
                Set-Service -Name "PM2" -StartupType Automatic
                Write-Success "PM2 服務已設定為自動啟動"
            }
            
            return $true
        }
        
        # 保存當前 PM2 進程列表
        Write-Info "保存當前 PM2 進程配置..."
        pm2 save 2>$null | Out-Null
        
        # 安裝 PM2 為 Windows 服務
        Write-Info "正在將 PM2 註冊為 Windows 服務..."
        pm2-installer install 2>$null | Out-Null
        
        # 等待服務安裝完成
        Start-Sleep -Seconds 5
        
        # 檢查服務是否安裝成功
        $newServiceStatus = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if ($newServiceStatus) {
            Write-Success "PM2 已成功註冊為 Windows 服務"
            
            # 設定服務為自動啟動
            Set-Service -Name "PM2" -StartupType Automatic
            Write-Success "PM2 服務已設定為自動啟動"
            
            # 啟動服務
            if ($newServiceStatus.Status -ne "Running") {
                Write-Info "正在啟動 PM2 服務..."
                Start-Service -Name "PM2"
                Write-Success "PM2 服務已啟動"
            }
            
            return $true
        } else {
            Write-Error "PM2 服務安裝失敗"
            return $false
        }
    }
    catch {
        Write-Error "配置 Windows 啟動項失敗: $($_.Exception.Message)"
        return $false
    }
}

function Remove-WindowsStartup {
    Write-Info "移除 Windows 系統啟動項..."
    
    try {
        # 檢查服務是否存在
        $serviceStatus = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if (-not $serviceStatus) {
            Write-Warning "PM2 服務不存在，無需移除"
            return $true
        }
        
        # 停止服務
        if ($serviceStatus.Status -eq "Running") {
            Write-Info "正在停止 PM2 服務..."
            Stop-Service -Name "PM2" -Force
            Write-Success "PM2 服務已停止"
        }
        
        # 卸載服務
        Write-Info "正在卸載 PM2 Windows 服務..."
        pm2-installer uninstall 2>$null | Out-Null
        
        # 等待卸載完成
        Start-Sleep -Seconds 3
        
        # 檢查服務是否已移除
        $removedServiceStatus = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if (-not $removedServiceStatus) {
            Write-Success "PM2 Windows 服務已成功移除"
            return $true
        } else {
            Write-Error "PM2 服務移除失敗"
            return $false
        }
    }
    catch {
        Write-Error "移除 Windows 啟動項失敗: $($_.Exception.Message)"
        return $false
    }
}

function Show-ServiceStatus {
    Write-Info "顯示當前服務狀態..."
    
    # 檢查 PM2 服務
    $pm2Service = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
    if ($pm2Service) {
        Write-ColorOutput "`nPM2 Windows 服務狀態：" "Magenta"
        Write-ColorOutput "  服務名稱: PM2" "White"
        Write-ColorOutput "  運行狀態: $($pm2Service.Status)" "Green"
        Write-ColorOutput "  啟動類型: $($pm2Service.StartType)" "White"
        Write-ColorOutput "  顯示名稱: $($pm2Service.DisplayName)" "Gray"
    } else {
        Write-ColorOutput "`nPM2 Windows 服務狀態：" "Magenta"
        Write-ColorOutput "  服務狀態: 未安裝" "Red"
    }
    
    # 檢查 PM2 進程
    try {
        Write-ColorOutput "`nPM2 進程狀態：" "Magenta"
        pm2 list
    }
    catch {
        Write-Error "無法獲取 PM2 進程狀態"
    }
}

# 主程序
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== Uptime Kuma Windows 啟動項配置工具 ===" "Magenta"
        Write-ColorOutput "此工具可以將 PM2 和 Uptime Kuma 配置為 Windows 服務" "Gray"
        Write-ColorOutput "配置後，服務將在系統重新啟動時自動運行" "Gray"
        
        # 顯示當前狀態
        Show-ServiceStatus
        
        # 詢問用戶操作
        Write-ColorOutput "`n請選擇要執行的操作：" "Yellow"
        Write-ColorOutput "1. 配置 Windows 系統啟動項" "White"
        Write-ColorOutput "2. 移除 Windows 系統啟動項" "White"
        Write-ColorOutput "3. 顯示服務狀態" "White"
        Write-ColorOutput "4. 退出" "White"
        
        do {
            $choice = Read-Host "`n請輸入選項 (1-4)"
        } while ($choice -notmatch "^[1-4]$")
        
        switch ($choice) {
            "1" {
                $confirm = Ask-UserPreference "確定要配置 Windows 系統啟動項嗎？" "Y"
                if ($confirm) {
                    $success = Configure-WindowsStartup
                    if ($success) {
                        Write-Success "`n✅ Windows 系統啟動項配置成功！"
                        Write-Info "Uptime Kuma 現在會在系統重新啟動後自動運行"
                    }
                }
            }
            "2" {
                $confirm = Ask-UserPreference "確定要移除 Windows 系統啟動項嗎？`n這將停止並卸載 PM2 Windows 服務。" "N"
                if ($confirm) {
                    $success = Remove-WindowsStartup
                    if ($success) {
                        Write-Success "`n✅ Windows 系統啟動項已成功移除！"
                        Write-Info "PM2 服務已停止，系統重啟後不會自動運行"
                    }
                }
            }
            "3" {
                Show-ServiceStatus
            }
            "4" {
                Write-Info "退出程序"
                return
            }
        }
        
        Write-ColorOutput "`n按任意鍵退出..." "Yellow"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
    }
    catch {
        Write-Error "執行過程中發生錯誤: $($_.Exception.Message)"
        Write-ColorOutput "`n按任意鍵退出..." "Red"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 1
    }
}

# 執行主程序
Main
