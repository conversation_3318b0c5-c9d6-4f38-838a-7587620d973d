# SYSTEM 帳戶 PM2 路徑問題修復

## 🐛 問題描述

在系統啟動時（未登入狀態），Windows 任務排程器以 SYSTEM 帳戶執行啟動腳本，但無法找到 `pm2` 命令：

```
[2025-07-22 11:25:04] [ERROR] 啟動 PM2 服務時發生錯誤: 無法辨識 'pm2' 詞彙是否為 Cmdlet、函數、指令檔或可執行程式的名稱。
```

## 🔍 問題分析

### 根本原因
1. **用戶級別安裝**：PM2 通常通過 `npm install -g pm2` 安裝在用戶級別路徑
2. **SYSTEM 帳戶限制**：SYSTEM 帳戶無法訪問用戶級別的環境變數和路徑
3. **PATH 環境變數**：SYSTEM 帳戶的 PATH 不包含用戶的 npm 全域安裝路徑

### 常見的 PM2 安裝路徑
- `%APPDATA%\npm\pm2.cmd` (用戶級別)
- `%ProgramFiles%\nodejs\pm2.cmd` (系統級別，較少見)
- `%ProgramFiles%\nodejs\node_modules\.bin\pm2.cmd`

## ✅ 修復方案

### 1. 智能路徑檢測
實現多層次的路徑檢測機制：

```powershell
# 尋找 Node.js 安裝路徑
function Find-NodePath {
    $possibleNodePaths = @(
        "${env:ProgramFiles}\nodejs\node.exe",
        "${env:ProgramFiles(x86)}\nodejs\node.exe",
        "C:\Program Files\nodejs\node.exe",
        "C:\Program Files (x86)\nodejs\node.exe"
    )
    
    foreach ($path in $possibleNodePaths) {
        if (Test-Path $path) {
            return Split-Path $path -Parent
        }
    }
}
```

### 2. 多路徑 PM2 檢測
```powershell
function Find-PM2Path {
    # 基於 Node.js 路徑的檢測
    $nodeDir = Find-NodePath
    if ($nodeDir) {
        $possiblePaths += @(
            "$nodeDir\pm2.cmd",
            "$nodeDir\node_modules\.bin\pm2.cmd"
        )
    }
    
    # 常見路徑檢測
    $possiblePaths += @(
        "${env:APPDATA}\npm\pm2.cmd",
        "C:\Users\<USER>\AppData\Roaming\npm\pm2.cmd"
    )
    
    # 直接 Node.js 執行方式
    $pm2JSPath = "$nodeDir\node_modules\pm2\bin\pm2"
    if (Test-Path $pm2JSPath) {
        return "$nodeDir\node.exe `"$pm2JSPath`""
    }
}
```

### 3. 動態 PM2 執行函數
```powershell
# 創建適應性的執行函數
$script:pm2Command = if ($pm2Path -like "*node.exe*") {
    # Node.js 直接執行方式
    { param($args) Invoke-Expression "$pm2Path $args" }
} else {
    # 直接執行檔方式
    { param($args) & $pm2Path $args }
}
```

### 4. 增強的環境變數設定
```powershell
function Set-EnvironmentVariables {
    # 獲取系統級別和用戶級別的環境變數
    $systemPath = [System.Environment]::GetEnvironmentVariable("Path", "Machine")
    $userPath = [System.Environment]::GetEnvironmentVariable("Path", "User")
    
    # 常見的 npm 全域安裝路徑
    $possibleNpmPaths = @(
        "${env:APPDATA}\npm",
        "${env:ProgramFiles}\nodejs\node_modules\.bin",
        "C:\Users\<USER>\AppData\Roaming\npm"
    )
    
    # 建立完整的 PATH
    $newPath = @($systemPath, $userPath, $nodePath) + $possibleNpmPaths -join ";"
    $env:PATH = $newPath
}
```

## 🔧 具體修復內容

### 修復前的問題
- ❌ 只依賴 PATH 環境變數尋找 PM2
- ❌ 沒有考慮 SYSTEM 帳戶的限制
- ❌ 沒有備用的執行方案

### 修復後的改進
- ✅ **多層次路徑檢測**：從 Node.js 路徑開始，逐步檢測可能的 PM2 位置
- ✅ **智能執行方式**：支援直接執行檔和 Node.js 腳本兩種方式
- ✅ **增強的環境變數**：合併系統和用戶級別的 PATH
- ✅ **詳細的日誌記錄**：記錄檢測過程和執行結果
- ✅ **錯誤恢復機制**：多種備用方案確保服務啟動

## 📝 執行流程

### 新的啟動流程
1. **尋找 Node.js 路徑** → 確定 Node.js 安裝位置
2. **檢測 PM2 執行檔** → 多路徑檢測 PM2 位置
3. **創建執行函數** → 根據檢測結果創建適應性執行函數
4. **測試 PM2 功能** → 驗證 PM2 是否可正常執行
5. **執行 PM2 命令** → 使用動態函數執行 PM2 操作

### 日誌輸出示例
```
[2025-07-22 11:25:03] [INFO] 尋找 Node.js 安裝路徑...
[2025-07-22 11:25:03] [INFO] 找到 Node.js: C:\Program Files\nodejs
[2025-07-22 11:25:03] [INFO] 尋找 PM2 執行檔...
[2025-07-22 11:25:03] [INFO] 找到 PM2: C:\Users\<USER>\AppData\Roaming\npm\pm2.cmd
[2025-07-22 11:25:04] [INFO] PM2 版本: 5.3.0
[2025-07-22 11:25:04] [INFO] PM2 路徑: C:\Users\<USER>\AppData\Roaming\npm\pm2.cmd
```

## 🎯 解決方案優勢

### 穩定性提升
- **多重備援**：多種路徑檢測方式，確保找到 PM2
- **適應性強**：支援不同的 PM2 安裝方式
- **錯誤處理**：完善的異常處理和日誌記錄

### 兼容性改善
- **系統帳戶兼容**：專門針對 SYSTEM 帳戶的限制進行優化
- **多版本支援**：支援不同版本的 Node.js 和 PM2 安裝
- **路徑靈活性**：不依賴特定的安裝路徑

## 🧪 測試建議

### 測試場景
1. **SYSTEM 帳戶測試**：使用 `psexec -s` 模擬 SYSTEM 帳戶執行
2. **不同安裝路徑**：測試 PM2 安裝在不同位置的情況
3. **重新啟動測試**：實際重新啟動系統驗證自動啟動功能

### 測試命令
```powershell
# 模擬 SYSTEM 帳戶執行
psexec -s powershell.exe -ExecutionPolicy Bypass -File "Start-UptimeKuma-Service.ps1"

# 檢查任務排程器狀態
Get-ScheduledTask -TaskName "UptimeKuma-AutoStart" | Get-ScheduledTaskInfo
```

這個修復確保了 Uptime Kuma 服務能夠在 Windows 系統重新啟動後，以 SYSTEM 帳戶身份成功自動啟動，解決了路徑檢測和權限問題。
