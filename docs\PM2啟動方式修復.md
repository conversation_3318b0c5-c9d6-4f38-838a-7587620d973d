# PM2 啟動方式修復說明

## 🐛 問題描述

執行腳本時出現以下錯誤：
```
ℹ 創建 PM2 生態系統配置文件...
✗ 創建 PM2 配置文件失敗: 找不到磁碟機。名為 ' added 692 packages, and audited 693 packages in 1m...' 的磁碟機不存在。
```

## 🔍 問題分析

1. **複雜的生態系統配置文件**：原腳本嘗試創建複雜的 PM2 ecosystem.config.json 文件
2. **路徑解析問題**：在創建配置文件時，路徑解析出現問題
3. **不符合官方推薦**：官方教學推薦使用簡單的命令行方式啟動

## ✅ 修復方案

根據 Uptime Kuma 官方教學，採用推薦的簡單啟動方式：

### 官方推薦命令
```bash
# Install PM2 if you don't have it:
npm install pm2 -g && pm2 install pm2-logrotate

# Start Server
pm2 start server/server.js --name uptime-kuma
```

## 🔧 具體修復

### 1. 移除複雜的生態系統配置文件函數
```powershell
# 移除 New-PM2EcosystemFile 函數
# 替換為簡單的 Install-PM2LogRotate 函數
function Install-PM2LogRotate {
    Write-Info "安裝 PM2 日誌輪轉插件..."
    
    try {
        pm2 install pm2-logrotate
        Write-Success "PM2 日誌輪轉插件安裝完成"
    }
    catch {
        Write-Warning "PM2 日誌輪轉插件安裝失敗，但不影響主要功能: $($_.Exception.Message)"
    }
}
```

### 2. 簡化服務啟動函數
```powershell
function Start-UptimeKumaService {
    param([string]$UptimeKumaPath)
    
    Write-Info "啟動 Uptime Kuma 服務..."
    
    try {
        # 切換到 Uptime Kuma 目錄
        Set-Location $UptimeKumaPath
        
        # 停止現有的 uptime-kuma 進程（如果存在）
        pm2 delete uptime-kuma 2>$null | Out-Null
        
        # 使用官方推薦的方式啟動服務
        pm2 start server/server.js --name uptime-kuma
        pm2 save
        
        Write-Success "Uptime Kuma 服務已啟動"
    }
    catch {
        Write-Error "啟動 Uptime Kuma 服務失敗: $($_.Exception.Message)"
        throw
    }
}
```

### 3. 更新主程序調用
```powershell
# 修復前
$configPath = New-PM2EcosystemFile -UptimeKumaPath $uptimeKumaPath
Start-UptimeKumaService -ConfigPath $configPath

# 修復後
Install-PM2LogRotate
Start-UptimeKumaService -UptimeKumaPath $uptimeKumaPath
```

## 🎯 修復優點

1. **簡化邏輯**：移除複雜的配置文件創建過程
2. **符合官方推薦**：使用官方教學中的啟動方式
3. **減少錯誤**：避免路徑解析和文件創建相關的錯誤
4. **更好的維護性**：代碼更簡潔，更容易維護

## 📝 啟動流程

修復後的啟動流程：
1. 安裝 PM2 日誌輪轉插件（可選，失敗不影響主功能）
2. 切換到 Uptime Kuma 安裝目錄
3. 停止現有的 uptime-kuma 進程（如果存在）
4. 使用 `pm2 start server/server.js --name uptime-kuma` 啟動服務
5. 保存 PM2 進程列表

這樣的方式更加穩定可靠，符合官方最佳實踐。
