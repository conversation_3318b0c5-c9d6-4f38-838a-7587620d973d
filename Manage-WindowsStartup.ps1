#Requires -Version 5.1

<#
.SYNOPSIS
    管理 Uptime Kuma Windows 系統啟動項
.DESCRIPTION
    此腳本用於管理 Uptime Kuma 在 Windows 系統中的自動啟動配置
    使用 Windows 任務排程器實現系統啟動時自動運行
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 任務名稱
$TaskName = "UptimeKuma-AutoStart"

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 檢查任務是否存在
function Test-WindowsStartup {
    try {
        $task = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        return $null -ne $task
    }
    catch {
        return $false
    }
}

# 安裝 Windows 啟動項
function Install-WindowsStartup {
    Write-Info "正在安裝 Windows 系統啟動項..."
    
    try {
        # 檢查啟動腳本是否存在
        $startupScript = Join-Path $PSScriptRoot "Start-UptimeKuma-Service.ps1"
        if (-not (Test-Path $startupScript)) {
            Write-Error "找不到啟動腳本: $startupScript"
            return $false
        }
        
        # 檢查是否已經存在任務
        if (Test-WindowsStartup) {
            Write-Warning "啟動項已存在，將先移除舊的任務"
            Remove-WindowsStartup
        }
        
        # 創建任務排程器任務
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -File `"$startupScript`""
        
        # 設定觸發器：系統啟動時運行
        $trigger = New-ScheduledTaskTrigger -AtStartup
        
        # 設定主體：以系統管理員身份運行
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
        
        # 設定設置
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd
        
        # 註冊任務
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "自動啟動 Uptime Kuma 監控服務" | Out-Null
        
        Write-Success "Windows 系統啟動項安裝成功"
        Write-Info "任務名稱: $TaskName"
        Write-Info "Uptime Kuma 將在系統重新啟動後自動啟動"
        
        return $true
    }
    catch {
        Write-Error "安裝 Windows 啟動項失敗: $($_.Exception.Message)"
        return $false
    }
}

# 移除 Windows 啟動項
function Remove-WindowsStartup {
    Write-Info "正在移除 Windows 系統啟動項..."
    
    try {
        if (Test-WindowsStartup) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            Write-Success "Windows 系統啟動項已移除"
        } else {
            Write-Warning "未找到啟動項，無需移除"
        }
        
        return $true
    }
    catch {
        Write-Error "移除 Windows 啟動項失敗: $($_.Exception.Message)"
        return $false
    }
}

# 顯示啟動項狀態
function Show-StartupStatus {
    Write-Info "檢查 Windows 系統啟動項狀態..."
    
    try {
        if (Test-WindowsStartup) {
            $task = Get-ScheduledTask -TaskName $TaskName
            Write-Success "啟動項已安裝"
            Write-ColorOutput "  任務名稱: $($task.TaskName)" "Gray"
            Write-ColorOutput "  狀態: $($task.State)" "Gray"
            Write-ColorOutput "  描述: $($task.Description)" "Gray"
            
            # 顯示任務詳細信息
            $taskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
            Write-ColorOutput "  上次運行時間: $($taskInfo.LastRunTime)" "Gray"
            Write-ColorOutput "  下次運行時間: $($taskInfo.NextRunTime)" "Gray"
            Write-ColorOutput "  上次運行結果: $($taskInfo.LastTaskResult)" "Gray"
        } else {
            Write-Warning "啟動項未安裝"
        }
    }
    catch {
        Write-Error "檢查啟動項狀態失敗: $($_.Exception.Message)"
    }
}

# 測試啟動腳本
function Test-StartupScript {
    Write-Info "測試啟動腳本..."
    
    try {
        $startupScript = Join-Path $PSScriptRoot "Start-UptimeKuma-Service.ps1"
        if (Test-Path $startupScript) {
            Write-Success "啟動腳本存在: $startupScript"
            
            # 檢查腳本語法
            $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $startupScript -Raw), [ref]$null)
            Write-Success "啟動腳本語法正確"
            
            return $true
        } else {
            Write-Error "啟動腳本不存在: $startupScript"
            return $false
        }
    }
    catch {
        Write-Error "測試啟動腳本失敗: $($_.Exception.Message)"
        return $false
    }
}

# 顯示幫助信息
function Show-Help {
    Write-ColorOutput "`n=== Uptime Kuma Windows 啟動項管理工具 ===" "Magenta"
    Write-ColorOutput "此工具用於管理 Uptime Kuma 在 Windows 系統中的自動啟動配置`n" "White"
    
    Write-ColorOutput "可用操作：" "Yellow"
    Write-ColorOutput "  install  - 安裝系統啟動項" "White"
    Write-ColorOutput "  remove   - 移除系統啟動項" "White"
    Write-ColorOutput "  status   - 顯示啟動項狀態" "White"
    Write-ColorOutput "  test     - 測試啟動腳本" "White"
    Write-ColorOutput "  help     - 顯示此幫助信息" "White"
    
    Write-ColorOutput "`n使用方法：" "Yellow"
    Write-ColorOutput "  .\Manage-WindowsStartup.ps1 [操作]" "Gray"
    Write-ColorOutput "  例如: .\Manage-WindowsStartup.ps1 install" "Gray"
    
    Write-ColorOutput "`n注意事項：" "Yellow"
    Write-ColorOutput "  - 此腳本需要管理員權限才能運行" "White"
    Write-ColorOutput "  - 啟動項使用 Windows 任務排程器實現" "White"
    Write-ColorOutput "  - 服務將在系統啟動時自動運行，無需用戶登入" "White"
}

# 主函數
function Main {
    param([string]$Action)
    
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" $Action"
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        switch ($Action.ToLower()) {
            "install" {
                if (Install-WindowsStartup) {
                    Show-StartupStatus
                }
            }
            "remove" {
                Remove-WindowsStartup
            }
            "status" {
                Show-StartupStatus
            }
            "test" {
                Test-StartupScript
            }
            "help" {
                Show-Help
            }
            default {
                if ([string]::IsNullOrEmpty($Action)) {
                    Show-Help
                } else {
                    Write-Error "未知的操作: $Action"
                    Show-Help
                }
            }
        }
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "執行過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主函數
Main -Action $args[0]
