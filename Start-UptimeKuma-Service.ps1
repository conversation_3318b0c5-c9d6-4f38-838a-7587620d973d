#Requires -Version 5.1

<#
.SYNOPSIS
    Windows 系統啟動時自動啟動 Uptime Kuma 服務
.DESCRIPTION
    此腳本用於在 Windows 系統啟動時自動啟動 PM2 和 Uptime Kuma 服務
    通過 Windows 任務排程器調用
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Continue"

# 日誌文件路徑
$logPath = "C:\uptime-kuma\logs\startup.log"

# 日誌函數
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # 確保日誌目錄存在
    $logDir = Split-Path $logPath -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    # 寫入日誌文件
    Add-Content -Path $logPath -Value $logMessage
    
    # 同時輸出到控制台（如果在交互模式下）
    if ($Host.UI.RawUI.WindowTitle) {
        Write-Host $logMessage
    }
}

# 等待網路連接
function Wait-NetworkConnection {
    Write-Log "等待網路連接..."
    $maxWait = 60  # 最多等待60秒
    $waited = 0
    
    while ($waited -lt $maxWait) {
        try {
            $ping = Test-NetConnection -ComputerName "*******" -Port 53 -InformationLevel Quiet -WarningAction SilentlyContinue
            if ($ping) {
                Write-Log "網路連接已建立"
                return $true
            }
        }
        catch {
            # 忽略網路測試錯誤
        }
        
        Start-Sleep -Seconds 5
        $waited += 5
        Write-Log "等待網路連接... ($waited/$maxWait 秒)"
    }
    
    Write-Log "網路連接等待超時，繼續啟動服務" "WARN"
    return $false
}

# 設定環境變數
function Set-EnvironmentVariables {
    Write-Log "設定環境變數..."

    try {
        # 設定 NODE_NO_WARNINGS 以忽略 Node.js 棄用警告
        $env:NODE_NO_WARNINGS = "1"

        # 獲取系統級別的環境變數
        $systemPath = [System.Environment]::GetEnvironmentVariable("Path", "Machine")
        $userPath = [System.Environment]::GetEnvironmentVariable("Path", "User")

        # 確保 PATH 包含必要的路徑
        $nodePath = "${env:ProgramFiles}\nodejs"
        $npmGlobalPath = "${env:ProgramFiles}\nodejs\node_modules\npm\bin"

        # 常見的 npm 全域安裝路徑
        $possibleNpmPaths = @(
            "${env:APPDATA}\npm",
            "${env:ProgramFiles}\nodejs\node_modules\.bin",
            "${env:ProgramFiles}\nodejs",
            "C:\Users\<USER>\AppData\Roaming\npm"
        )

        # 建立完整的 PATH
        $newPath = @($systemPath, $userPath, $nodePath, $npmGlobalPath) + $possibleNpmPaths -join ";"
        $env:PATH = $newPath

        Write-Log "PATH 已更新: $env:PATH"
        Write-Log "環境變數設定完成"
        return $true
    }
    catch {
        Write-Log "設定環境變數失敗: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 尋找 Node.js 安裝路徑
function Find-NodePath {
    Write-Log "尋找 Node.js 安裝路徑..."

    $possibleNodePaths = @(
        "${env:ProgramFiles}\nodejs\node.exe",
        "${env:ProgramFiles(x86)}\nodejs\node.exe",
        "C:\Program Files\nodejs\node.exe",
        "C:\Program Files (x86)\nodejs\node.exe"
    )

    foreach ($path in $possibleNodePaths) {
        if (Test-Path $path) {
            $nodeDir = Split-Path $path -Parent
            Write-Log "找到 Node.js: $nodeDir"
            return $nodeDir
        }
    }

    # 嘗試在 PATH 中尋找
    try {
        $nodeInPath = Get-Command node -ErrorAction SilentlyContinue
        if ($nodeInPath) {
            $nodeDir = Split-Path $nodeInPath.Source -Parent
            Write-Log "在 PATH 中找到 Node.js: $nodeDir"
            return $nodeDir
        }
    }
    catch {
        # 忽略錯誤
    }

    Write-Log "未找到 Node.js" "ERROR"
    return $null
}

# 尋找 PM2 執行檔
function Find-PM2Path {
    Write-Log "尋找 PM2 執行檔..."

    # 首先尋找 Node.js 路徑
    $nodeDir = Find-NodePath

    # 可能的 PM2 路徑
    $possiblePaths = @()

    if ($nodeDir) {
        $possiblePaths += @(
            "$nodeDir\pm2.cmd",
            "$nodeDir\pm2",
            "$nodeDir\node_modules\.bin\pm2.cmd",
            "$nodeDir\node_modules\.bin\pm2"
        )
    }

    # 添加其他常見路徑
    $possiblePaths += @(
        "${env:APPDATA}\npm\pm2.cmd",
        "${env:APPDATA}\npm\pm2",
        "C:\Users\<USER>\AppData\Roaming\npm\pm2.cmd",
        "C:\Users\<USER>\AppData\Roaming\npm\pm2",
        "${env:ProgramFiles}\nodejs\pm2.cmd",
        "${env:ProgramFiles}\nodejs\pm2"
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            Write-Log "找到 PM2: $path"
            return $path
        }
    }

    # 嘗試在 PATH 中尋找
    try {
        $pm2InPath = Get-Command pm2 -ErrorAction SilentlyContinue
        if ($pm2InPath) {
            Write-Log "在 PATH 中找到 PM2: $($pm2InPath.Source)"
            return $pm2InPath.Source
        }
    }
    catch {
        # 忽略錯誤
    }

    # 最後嘗試：使用 Node.js 直接執行 PM2
    if ($nodeDir) {
        $pm2JSPath = "$nodeDir\node_modules\pm2\bin\pm2"
        if (Test-Path $pm2JSPath) {
            Write-Log "找到 PM2 JavaScript 文件: $pm2JSPath"
            return "$nodeDir\node.exe `"$pm2JSPath`""
        }
    }

    Write-Log "未找到 PM2 執行檔" "ERROR"
    return $null
}

# 啟動 PM2 服務
function Start-PM2Service {
    Write-Log "啟動 PM2 服務..."

    try {
        # 切換到 Uptime Kuma 目錄
        $uptimeKumaPath = "C:\uptime-kuma"
        if (-not (Test-Path $uptimeKumaPath)) {
            Write-Log "Uptime Kuma 安裝目錄不存在: $uptimeKumaPath" "ERROR"
            return $false
        }

        Set-Location $uptimeKumaPath
        Write-Log "切換到目錄: $uptimeKumaPath"

        # 尋找 PM2 執行檔
        $pm2Path = Find-PM2Path
        if (-not $pm2Path) {
            Write-Log "無法找到 PM2 執行檔" "ERROR"
            return $false
        }

        # 創建 PM2 執行函數
        $script:pm2Command = if ($pm2Path -like "*node.exe*") {
            # 如果是 Node.js 執行方式
            { param($args) Invoke-Expression "$pm2Path $args" }
        } else {
            # 如果是直接執行檔
            { param($args) & $pm2Path $args }
        }

        # 檢查 PM2 是否可用
        try {
            $pm2Version = & $script:pm2Command "--version" 2>$null
            if (-not $pm2Version) {
                Write-Log "PM2 無法執行" "ERROR"
                return $false
            }

            Write-Log "PM2 版本: $pm2Version"
            Write-Log "PM2 路徑: $pm2Path"
        }
        catch {
            Write-Log "PM2 執行測試失敗: $($_.Exception.Message)" "ERROR"
            return $false
        }
        
        # 恢復保存的 PM2 進程
        Write-Log "恢復 PM2 進程..."
        try {
            & $script:pm2Command "resurrect" 2>$null | Out-Null
        }
        catch {
            Write-Log "PM2 resurrect 執行失敗: $($_.Exception.Message)" "WARN"
        }

        # 等待服務啟動
        Start-Sleep -Seconds 5

        # 檢查 Uptime Kuma 是否在運行
        try {
            $pm2ListOutput = & $script:pm2Command "list" 2>$null
            $uptimeKumaStatus = $pm2ListOutput | Select-String "uptime-kuma.*online"

            if ($uptimeKumaStatus) {
                Write-Log "Uptime Kuma 服務已成功啟動"
                return $true
            } else {
                # 如果沒有運行，嘗試手動啟動
                Write-Log "嘗試手動啟動 Uptime Kuma 服務..."
                & $script:pm2Command "start server/server.js --name uptime-kuma" 2>$null | Out-Null
                & $script:pm2Command "save" 2>$null | Out-Null

                Start-Sleep -Seconds 3
                $pm2ListOutput = & $script:pm2Command "list" 2>$null
                $uptimeKumaStatus = $pm2ListOutput | Select-String "uptime-kuma.*online"

                if ($uptimeKumaStatus) {
                    Write-Log "Uptime Kuma 服務手動啟動成功"
                    return $true
                } else {
                    Write-Log "Uptime Kuma 服務啟動失敗" "ERROR"
                    Write-Log "PM2 列表輸出: $pm2ListOutput" "ERROR"
                    return $false
                }
            }
        }
        catch {
            Write-Log "PM2 命令執行失敗: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "啟動 PM2 服務時發生錯誤: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 主函數
function Main {
    Write-Log "=== Uptime Kuma 系統啟動腳本開始執行 ==="
    
    try {
        # 等待系統完全啟動
        Write-Log "等待系統完全啟動..."
        Start-Sleep -Seconds 10
        
        # 等待網路連接
        Wait-NetworkConnection
        
        # 設定環境變數
        if (-not (Set-EnvironmentVariables)) {
            Write-Log "環境變數設定失敗，退出" "ERROR"
            return
        }
        
        # 啟動 PM2 服務
        if (Start-PM2Service) {
            Write-Log "=== Uptime Kuma 服務啟動成功 ==="
        } else {
            Write-Log "=== Uptime Kuma 服務啟動失敗 ===" "ERROR"
        }
    }
    catch {
        Write-Log "主函數執行時發生錯誤: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "=== 啟動腳本執行完成 ==="
}

# 執行主函數
Main
