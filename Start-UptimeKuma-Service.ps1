#Requires -Version 5.1

<#
.SYNOPSIS
    Windows 系統啟動時自動啟動 Uptime Kuma 服務
.DESCRIPTION
    此腳本用於在 Windows 系統啟動時自動啟動 PM2 和 Uptime Kuma 服務
    通過 Windows 任務排程器調用
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Continue"

# 日誌文件路徑
$logPath = "C:\uptime-kuma\logs\startup.log"

# 日誌函數
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # 確保日誌目錄存在
    $logDir = Split-Path $logPath -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    # 寫入日誌文件
    Add-Content -Path $logPath -Value $logMessage
    
    # 同時輸出到控制台（如果在交互模式下）
    if ($Host.UI.RawUI.WindowTitle) {
        Write-Host $logMessage
    }
}

# 等待網路連接
function Wait-NetworkConnection {
    Write-Log "等待網路連接..."
    $maxWait = 60  # 最多等待60秒
    $waited = 0
    
    while ($waited -lt $maxWait) {
        try {
            $ping = Test-NetConnection -ComputerName "*******" -Port 53 -InformationLevel Quiet -WarningAction SilentlyContinue
            if ($ping) {
                Write-Log "網路連接已建立"
                return $true
            }
        }
        catch {
            # 忽略網路測試錯誤
        }
        
        Start-Sleep -Seconds 5
        $waited += 5
        Write-Log "等待網路連接... ($waited/$maxWait 秒)"
    }
    
    Write-Log "網路連接等待超時，繼續啟動服務" "WARN"
    return $false
}

# 設定環境變數
function Set-EnvironmentVariables {
    Write-Log "設定環境變數..."
    
    try {
        # 設定 NODE_NO_WARNINGS 以忽略 Node.js 棄用警告
        $env:NODE_NO_WARNINGS = "1"
        
        # 確保 PATH 包含必要的路徑
        $nodePath = "${env:ProgramFiles}\nodejs"
        $npmPath = "${env:APPDATA}\npm"
        
        if ($env:PATH -notlike "*$nodePath*") {
            $env:PATH = "$nodePath;$env:PATH"
        }
        
        if ($env:PATH -notlike "*$npmPath*") {
            $env:PATH = "$npmPath;$env:PATH"
        }
        
        Write-Log "環境變數設定完成"
        return $true
    }
    catch {
        Write-Log "設定環境變數失敗: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 啟動 PM2 服務
function Start-PM2Service {
    Write-Log "啟動 PM2 服務..."
    
    try {
        # 切換到 Uptime Kuma 目錄
        $uptimeKumaPath = "C:\uptime-kuma"
        if (-not (Test-Path $uptimeKumaPath)) {
            Write-Log "Uptime Kuma 安裝目錄不存在: $uptimeKumaPath" "ERROR"
            return $false
        }
        
        Set-Location $uptimeKumaPath
        Write-Log "切換到目錄: $uptimeKumaPath"
        
        # 檢查 PM2 是否可用
        $pm2Version = pm2 --version 2>$null
        if (-not $pm2Version) {
            Write-Log "PM2 未安裝或不可用" "ERROR"
            return $false
        }
        
        Write-Log "PM2 版本: $pm2Version"
        
        # 恢復保存的 PM2 進程
        Write-Log "恢復 PM2 進程..."
        pm2 resurrect 2>$null | Out-Null
        
        # 等待服務啟動
        Start-Sleep -Seconds 5
        
        # 檢查 Uptime Kuma 是否在運行
        $uptimeKumaStatus = pm2 list | Select-String "uptime-kuma.*online"
        
        if ($uptimeKumaStatus) {
            Write-Log "Uptime Kuma 服務已成功啟動"
            return $true
        } else {
            # 如果沒有運行，嘗試手動啟動
            Write-Log "嘗試手動啟動 Uptime Kuma 服務..."
            pm2 start server/server.js --name uptime-kuma 2>$null | Out-Null
            pm2 save 2>$null | Out-Null
            
            Start-Sleep -Seconds 3
            $uptimeKumaStatus = pm2 list | Select-String "uptime-kuma.*online"
            
            if ($uptimeKumaStatus) {
                Write-Log "Uptime Kuma 服務手動啟動成功"
                return $true
            } else {
                Write-Log "Uptime Kuma 服務啟動失敗" "ERROR"
                return $false
            }
        }
    }
    catch {
        Write-Log "啟動 PM2 服務時發生錯誤: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 主函數
function Main {
    Write-Log "=== Uptime Kuma 系統啟動腳本開始執行 ==="
    
    try {
        # 等待系統完全啟動
        Write-Log "等待系統完全啟動..."
        Start-Sleep -Seconds 10
        
        # 等待網路連接
        Wait-NetworkConnection
        
        # 設定環境變數
        if (-not (Set-EnvironmentVariables)) {
            Write-Log "環境變數設定失敗，退出" "ERROR"
            return
        }
        
        # 啟動 PM2 服務
        if (Start-PM2Service) {
            Write-Log "=== Uptime Kuma 服務啟動成功 ==="
        } else {
            Write-Log "=== Uptime Kuma 服務啟動失敗 ===" "ERROR"
        }
    }
    catch {
        Write-Log "主函數執行時發生錯誤: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "=== 啟動腳本執行完成 ==="
}

# 執行主函數
Main
