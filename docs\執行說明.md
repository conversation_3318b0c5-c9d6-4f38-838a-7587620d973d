# PowerShell 腳本執行說明

## ✅ 問題已解決！

腳本現在已經整合了自動權限提升功能，可以直接執行而無需手動處理執行策略問題。

## 🚀 執行方法

### 方法 1：直接執行 PowerShell 腳本（推薦）
直接雙擊 `Install-DevTools.ps1` 文件，腳本會：
- ✅ 自動檢測管理員權限
- ✅ 自動提升權限（如果需要）
- ✅ 自動繞過執行策略限制
- ✅ 執行完整的安裝流程

### 方法 2：使用批次檔案
直接雙擊執行 `Install-DevTools.bat` 文件（備用方案）

### 方法 3：使用 PowerShell 啟動器
右鍵點擊 `Run-Install.ps1` → 選擇「使用 PowerShell 執行」

### 方法 4：命令列執行
在任何 PowerShell 視窗中執行：
```powershell
.\Install-DevTools.ps1
```

## 🔧 技術改進

腳本現在使用了更優雅的權限提升機制：
```powershell
If (-not (Test-Admin)) {
    Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
    $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
    Start-Process powershell -Verb runAs -ArgumentList $arguments
    Exit
}
```

這個方法的優點：
- 🎯 自動檢測權限狀態
- 🔄 自動重新啟動並提升權限
- 🛡️ 自動繞過執行策略限制
- 📝 保留所有命令列參數

## 腳本功能
- 自動檢測並提升管理員權限
- 安裝 Chocolatey 套件管理器
- 安裝 Git、Node.js、PM2
- 安裝並配置 Uptime Kuma 監控工具
- 自動啟動服務並開啟瀏覽器

## 注意事項
- 腳本需要管理員權限才能正常運行
- 首次執行可能需要較長時間下載和安裝套件
- 確保網路連線正常
