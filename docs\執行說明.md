# PowerShell 腳本執行說明

## 問題描述
Windows 預設的 PowerShell 執行策略會阻止未簽名的腳本執行，導致出現以下錯誤：
```
因為這個系統上已停用指令碼執行，所以無法載入檔案。
```

## 解決方案

### 方法 1：使用批次檔案（推薦）
直接雙擊執行 `Install-DevTools.bat` 文件，這個批次檔案會：
- 自動繞過執行策略限制
- 執行主要的 PowerShell 腳本
- 自動提升管理員權限

### 方法 2：使用 PowerShell 啟動器
右鍵點擊 `Run-Install.ps1` → 選擇「使用 PowerShell 執行」

### 方法 3：手動執行命令
在 PowerShell 中執行以下命令：
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
.\Install-DevTools.ps1
```

### 方法 4：以管理員身份執行 PowerShell
1. 右鍵點擊「開始」按鈕
2. 選擇「Windows PowerShell (系統管理員)」
3. 執行以下命令：
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
cd "C:\Users\<USER>\Desktop"
.\Install-DevTools.ps1
```

## 腳本功能
- 自動檢測並提升管理員權限
- 安裝 Chocolatey 套件管理器
- 安裝 Git、Node.js、PM2
- 安裝並配置 Uptime Kuma 監控工具
- 自動啟動服務並開啟瀏覽器

## 注意事項
- 腳本需要管理員權限才能正常運行
- 首次執行可能需要較長時間下載和安裝套件
- 確保網路連線正常
