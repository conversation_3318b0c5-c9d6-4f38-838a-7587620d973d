# Windows 系統啟動配置說明

## 🎯 功能概述

在 Windows 系統中，`pm2 startup` 命令不被支援。為了實現 Uptime Kuma 在系統重新啟動後自動運行，我們使用 `pm2-installer` 將 PM2 註冊為 Windows 服務。

## 🔧 實現方案

### 使用的工具
- **pm2-installer**: 專門為 Windows 設計的 PM2 服務安裝器
- **Windows 服務管理**: 將 PM2 註冊為系統服務
- **自動啟動配置**: 設定服務為自動啟動類型

### 工作原理
1. **安裝 pm2-installer**: 全域安裝 PM2 Windows 服務安裝器
2. **保存 PM2 配置**: 使用 `pm2 save` 保存當前進程列表
3. **註冊 Windows 服務**: 使用 `pm2-installer install` 將 PM2 註冊為服務
4. **配置自動啟動**: 設定服務啟動類型為 Automatic
5. **啟動服務**: 立即啟動 PM2 服務

## 📋 使用方法

### 自動配置（推薦）
在執行 `Install-DevTools.ps1` 時，腳本會自動：
1. 安裝 pm2-installer
2. 詢問用戶是否要配置自動啟動
3. 如果用戶同意，自動配置 Windows 服務

### 手動配置
如果需要單獨配置或管理 Windows 啟動項：

```powershell
# 執行配置工具
.\Configure-WindowsStartup.ps1
```

配置工具提供以下選項：
- **配置 Windows 系統啟動項**: 將 PM2 註冊為 Windows 服務
- **移除 Windows 系統啟動項**: 卸載 PM2 Windows 服務
- **顯示服務狀態**: 查看當前服務和進程狀態

## 🛠️ 技術細節

### PM2 服務配置
```powershell
# 安裝為 Windows 服務
pm2-installer install

# 設定自動啟動
Set-Service -Name "PM2" -StartupType Automatic

# 啟動服務
Start-Service -Name "PM2"
```

### 服務管理命令
```powershell
# 檢查服務狀態
Get-Service -Name "PM2"

# 啟動服務
Start-Service -Name "PM2"

# 停止服務
Stop-Service -Name "PM2"

# 重啟服務
Restart-Service -Name "PM2"
```

### 卸載服務
```powershell
# 停止服務
Stop-Service -Name "PM2" -Force

# 卸載服務
pm2-installer uninstall
```

## 📊 服務狀態檢查

### 通過 PowerShell 檢查
```powershell
# 檢查 PM2 Windows 服務
Get-Service -Name "PM2"

# 檢查 PM2 進程列表
pm2 list

# 檢查 Uptime Kuma 進程
pm2 list | Select-String "uptime-kuma"
```

### 通過 Windows 服務管理器
1. 按 `Win + R`，輸入 `services.msc`
2. 在服務列表中找到 "PM2" 服務
3. 檢查服務狀態和啟動類型

## ⚠️ 注意事項

### 權限要求
- 配置 Windows 服務需要管理員權限
- 腳本會自動檢查並提升權限

### 相容性
- 支援 Windows 7 及以上版本
- 需要 Node.js 和 PM2 已正確安裝

### 故障排除
1. **服務安裝失敗**:
   - 確保以管理員身份運行
   - 檢查 pm2-installer 是否正確安裝
   - 查看 Windows 事件日誌

2. **服務無法啟動**:
   - 檢查 Node.js 路徑是否正確
   - 確保 PM2 配置文件存在
   - 檢查防火牆設定

3. **Uptime Kuma 未自動啟動**:
   - 確保已執行 `pm2 save`
   - 檢查 PM2 進程列表
   - 驗證服務是否正在運行

## 🔄 系統重啟測試

配置完成後，建議進行系統重啟測試：

1. **重新啟動電腦**
2. **等待系統完全啟動**
3. **檢查服務狀態**:
   ```powershell
   Get-Service -Name "PM2"
   pm2 list
   ```
4. **訪問 Uptime Kuma**: http://localhost:3001

## 📝 最佳實踐

1. **定期備份**: 定期執行 `pm2 save` 保存配置
2. **監控日誌**: 使用 `pm2 logs` 監控服務日誌
3. **更新維護**: 定期更新 PM2 和相關依賴
4. **安全考慮**: 確保防火牆正確配置

這樣的配置確保了 Uptime Kuma 在 Windows 系統中的穩定運行和自動啟動。
