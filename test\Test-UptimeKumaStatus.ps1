# 測試 Uptime Kuma 服務狀態

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

# 測試 PM2 服務狀態
function Test-PM2Status {
    Write-Info "檢查 PM2 服務狀態..."
    
    try {
        # 檢查 PM2 是否安裝
        $pm2Version = pm2 --version 2>$null
        if ($pm2Version) {
            Write-Success "PM2 版本: $pm2Version"
        } else {
            Write-Error "PM2 未安裝"
            return $false
        }
        
        # 檢查 uptime-kuma 服務狀態
        Write-Info "檢查 Uptime Kuma 服務狀態..."
        $uptimeKumaStatus = pm2 list | Select-String "uptime-kuma"
        
        if ($uptimeKumaStatus) {
            Write-Success "找到 Uptime Kuma 進程"
            Write-ColorOutput "狀態: $uptimeKumaStatus" "Gray"
            
            # 檢查是否在線
            $onlineStatus = pm2 list | Select-String "uptime-kuma.*online"
            if ($onlineStatus) {
                Write-Success "Uptime Kuma 服務正在運行中"
                return $true
            } else {
                Write-Warning "Uptime Kuma 服務存在但可能不在線"
                return $false
            }
        } else {
            Write-Error "未找到 Uptime Kuma 進程"
            return $false
        }
    }
    catch {
        Write-Error "檢查 PM2 狀態失敗: $($_.Exception.Message)"
        return $false
    }
}

# 測試 HTTP 連接
function Test-HTTPConnection {
    Write-Info "測試 HTTP 連接到 localhost:3001..."
    
    try {
        # 使用 Invoke-WebRequest 測試連接
        $response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 10 -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            Write-Success "HTTP 連接成功 (狀態碼: $($response.StatusCode))"
            Write-Success "Uptime Kuma 網頁服務正常運行"
            return $true
        } else {
            Write-Warning "HTTP 連接異常 (狀態碼: $($response.StatusCode))"
            return $false
        }
    }
    catch {
        Write-Error "HTTP 連接失敗: $($_.Exception.Message)"
        Write-Info "這可能表示服務尚未完全啟動，請稍等片刻再試"
        return $false
    }
}

# 顯示完整的 PM2 狀態
function Show-PM2FullStatus {
    Write-Info "顯示完整的 PM2 狀態..."
    
    try {
        Write-ColorOutput "`n=== PM2 進程列表 ===" "Magenta"
        pm2 list
        
        Write-ColorOutput "`n=== PM2 模組列表 ===" "Magenta"
        pm2 list | Select-String "Module" -A 10
        
    }
    catch {
        Write-Error "無法顯示 PM2 狀態: $($_.Exception.Message)"
    }
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== Uptime Kuma 服務狀態檢查 ===" "Magenta"
        
        $pm2Status = Test-PM2Status
        $httpStatus = Test-HTTPConnection
        
        Write-ColorOutput "`n=== 檢查結果 ===" "Magenta"
        
        if ($pm2Status -and $httpStatus) {
            Write-Success "✅ Uptime Kuma 服務完全正常！"
            Write-Info "您可以在瀏覽器中訪問: http://localhost:3001"
        } elseif ($pm2Status -and -not $httpStatus) {
            Write-Warning "⚠️ PM2 服務在運行，但 HTTP 連接失敗"
            Write-Info "服務可能正在啟動中，請稍等片刻再試"
        } elseif (-not $pm2Status -and $httpStatus) {
            Write-Warning "⚠️ HTTP 服務可用，但 PM2 狀態異常"
            Write-Info "服務可能通過其他方式運行"
        } else {
            Write-Error "❌ Uptime Kuma 服務未正常運行"
            Write-Info "請檢查安裝是否成功完成"
        }
        
        Show-PM2FullStatus
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "測試過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
