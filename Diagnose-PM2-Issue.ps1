# PM2 問題診斷腳本

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

# 顯示系統信息
function Show-SystemInfo {
    Write-Info "=== 系統信息 ==="
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    Write-ColorOutput "當前用戶: $($identity.Name)" "Gray"
    Write-ColorOutput "是否為系統帳戶: $($identity.IsSystem)" "Gray"
    Write-ColorOutput "PowerShell 版本: $($PSVersionTable.PSVersion)" "Gray"
    Write-ColorOutput "作業系統: $([System.Environment]::OSVersion.VersionString)" "Gray"
}

# 顯示環境變數
function Show-EnvironmentVariables {
    Write-Info "`n=== 環境變數 ==="
    Write-ColorOutput "PATH:" "Yellow"
    $env:PATH -split ";" | ForEach-Object { Write-ColorOutput "  $_" "Gray" }
    Write-ColorOutput "`nAPPDATA: $env:APPDATA" "Gray"
    Write-ColorOutput "ProgramFiles: $env:ProgramFiles" "Gray"
    Write-ColorOutput "USERPROFILE: $env:USERPROFILE" "Gray"
}

# 檢查 Node.js 安裝
function Test-NodeJS {
    Write-Info "`n=== Node.js 檢查 ==="
    
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Success "Node.js 版本: $nodeVersion"
            
            $nodeLocation = Get-Command node -ErrorAction SilentlyContinue
            if ($nodeLocation) {
                Write-Success "Node.js 位置: $($nodeLocation.Source)"
            }
        } else {
            Write-Error "Node.js 未安裝或不在 PATH 中"
        }
    }
    catch {
        Write-Error "Node.js 檢查失敗: $($_.Exception.Message)"
    }
    
    # 檢查 npm
    try {
        $npmVersion = npm --version 2>$null
        if ($npmVersion) {
            Write-Success "npm 版本: $npmVersion"
            
            $npmLocation = Get-Command npm -ErrorAction SilentlyContinue
            if ($npmLocation) {
                Write-Success "npm 位置: $($npmLocation.Source)"
            }
        } else {
            Write-Error "npm 未安裝或不在 PATH 中"
        }
    }
    catch {
        Write-Error "npm 檢查失敗: $($_.Exception.Message)"
    }
}

# 檢查 PM2 安裝
function Test-PM2Installation {
    Write-Info "`n=== PM2 安裝檢查 ==="
    
    # 檢查 PM2 命令
    try {
        $pm2Version = pm2 --version 2>$null
        if ($pm2Version) {
            Write-Success "PM2 版本: $pm2Version"
            
            $pm2Location = Get-Command pm2 -ErrorAction SilentlyContinue
            if ($pm2Location) {
                Write-Success "PM2 位置: $($pm2Location.Source)"
            }
        } else {
            Write-Error "PM2 命令不可用"
        }
    }
    catch {
        Write-Error "PM2 命令檢查失敗: $($_.Exception.Message)"
    }
    
    # 檢查 npx pm2
    try {
        $npxPm2Version = npx pm2 --version 2>$null
        if ($npxPm2Version) {
            Write-Success "npx pm2 版本: $npxPm2Version"
        } else {
            Write-Warning "npx pm2 不可用"
        }
    }
    catch {
        Write-Warning "npx pm2 檢查失敗: $($_.Exception.Message)"
    }
}

# 搜尋 PM2 文件
function Search-PM2Files {
    Write-Info "`n=== PM2 文件搜尋 ==="
    
    # 常見路徑
    $commonPaths = @(
        "${env:APPDATA}\npm",
        "C:\Windows\system32\config\systemprofile\AppData\Roaming\npm",
        "C:\Users\<USER>\AppData\Roaming\npm",
        "${env:ProgramFiles}\nodejs",
        "${env:ProgramFiles}\nodejs\node_modules\.bin"
    )
    
    foreach ($path in $commonPaths) {
        Write-ColorOutput "檢查目錄: $path" "Gray"
        if (Test-Path $path) {
            $pm2Files = Get-ChildItem -Path $path -Name "pm2*" -ErrorAction SilentlyContinue
            if ($pm2Files) {
                foreach ($file in $pm2Files) {
                    Write-Success "  找到: $path\$file"
                }
            } else {
                Write-ColorOutput "  無 PM2 文件" "Gray"
            }
        } else {
            Write-ColorOutput "  目錄不存在" "Gray"
        }
    }
    
    # 使用 where 命令搜尋
    Write-Info "使用 where 命令搜尋..."
    try {
        $whereResults = where.exe pm2 2>$null
        if ($whereResults) {
            foreach ($result in $whereResults) {
                Write-Success "where 找到: $result"
            }
        } else {
            Write-Warning "where 命令未找到 PM2"
        }
    }
    catch {
        Write-Warning "where 命令失敗: $($_.Exception.Message)"
    }
}

# 檢查 npm 全域套件
function Check-NPMGlobalPackages {
    Write-Info "`n=== npm 全域套件檢查 ==="
    
    try {
        $globalPackages = npm list -g --depth=0 2>$null
        if ($globalPackages) {
            Write-ColorOutput "npm 全域套件:" "Yellow"
            $globalPackages -split "`n" | ForEach-Object {
                if ($_ -match "pm2") {
                    Write-Success "  $_"
                } else {
                    Write-ColorOutput "  $_" "Gray"
                }
            }
        } else {
            Write-Warning "無法獲取 npm 全域套件列表"
        }
    }
    catch {
        Write-Error "npm 全域套件檢查失敗: $($_.Exception.Message)"
    }
    
    # 檢查 npm 全域安裝路徑
    try {
        $npmRoot = npm root -g 2>$null
        if ($npmRoot) {
            Write-Success "npm 全域根目錄: $npmRoot"
            
            $pm2Path = Join-Path $npmRoot "pm2"
            if (Test-Path $pm2Path) {
                Write-Success "PM2 模組目錄存在: $pm2Path"
            } else {
                Write-Warning "PM2 模組目錄不存在: $pm2Path"
            }
        }
    }
    catch {
        Write-Error "npm 全域根目錄檢查失敗: $($_.Exception.Message)"
    }
}

# 嘗試安裝 PM2
function Try-InstallPM2 {
    Write-Info "`n=== 嘗試安裝 PM2 ==="
    
    $choice = Read-Host "是否要嘗試重新安裝 PM2？ [Y/N]"
    if ($choice.ToUpper() -eq "Y") {
        try {
            Write-Info "正在安裝 PM2..."
            npm install -g pm2
            
            Write-Info "安裝完成，重新檢查..."
            Start-Sleep -Seconds 2
            Test-PM2Installation
        }
        catch {
            Write-Error "PM2 安裝失敗: $($_.Exception.Message)"
        }
    }
}

# 主函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== PM2 問題診斷工具 ===" "Magenta"
        
        Show-SystemInfo
        Show-EnvironmentVariables
        Test-NodeJS
        Test-PM2Installation
        Search-PM2Files
        Check-NPMGlobalPackages
        Try-InstallPM2
        
        Write-ColorOutput "`n=== 診斷完成 ===" "Magenta"
        Write-Info "請將以上信息提供給技術支援以協助解決問題"
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "診斷過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
