# PowerShell 腳本錯誤修復說明

## 🐛 錯誤描述

執行腳本時出現以下錯誤：
```
✗ 安裝過程中發生錯誤: 無法繫結 'ForegroundColor' 參數。無法將 "*50" 值轉換為 "System.ConsoleColor" 型別。
```

## 🔍 問題分析

錯誤原因是在調用 `Write-ColorOutput` 函數時，參數順序和格式不正確：

### 錯誤的調用方式：
```powershell
Write-ColorOutput "="*50 "Magenta"
```

PowerShell 將 `"="*50` 和 `"Magenta"` 解析為兩個獨立的參數，導致：
- 第一個參數 `$Message` = `"="`
- 第二個參數 `$Color` = `"*50"`（這不是有效的顏色名稱）
- 第三個參數被忽略

## ✅ 修復方案

### 正確的調用方式：
```powershell
Write-ColorOutput ("="*50) "Magenta"
```

使用括號確保 `"="*50` 被正確計算為一個完整的字符串參數。

## 🔧 具體修復

### 修復位置 1：主程序中的分隔線
```powershell
# 修復前
Write-ColorOutput "="*50 "Magenta"

# 修復後  
Write-ColorOutput ("="*50) "Magenta"
```

### 修復位置 2：安裝摘要函數中的分隔線
```powershell
# 修復前
Write-ColorOutput "`n" + "="*60 "Magenta"
Write-ColorOutput "="*60 "Magenta"

# 修復後
Write-ColorOutput ("`n" + "="*60) "Magenta"
Write-ColorOutput ("="*60) "Magenta"
```

### 修復位置 3：清理重複的錯誤信息
```powershell
# 修復前（重複顯示錯誤信息）
catch {
    Write-Error "安裝過程中發生錯誤: $($_.Exception.Message)"
    Write-ColorOutput "`n按任意鍵退出..." "Red"
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Read-Host -Prompt "安裝過程中發生錯誤: $($_.Exception.Message)"
    exit 1
}

# 修復後（簡潔的錯誤處理）
catch {
    Write-Error "安裝過程中發生錯誤: $($_.Exception.Message)"
    Write-ColorOutput "`n按任意鍵退出..." "Red"
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}
```

## 🧪 測試驗證

創建了測試腳本 `test/Test-ColorOutput.ps1` 來驗證修復效果。

## 📝 經驗教訓

1. **參數解析**：PowerShell 中的字符串操作需要注意參數邊界
2. **括號使用**：使用括號確保表達式被正確計算
3. **錯誤處理**：避免重複的錯誤信息顯示
4. **測試重要性**：每次修改後都應該進行測試驗證

現在腳本應該可以正常執行，不會再出現 ForegroundColor 參數錯誤。
