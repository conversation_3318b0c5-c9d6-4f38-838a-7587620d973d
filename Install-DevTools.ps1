#Requires -Version 5.1

<#
.SYNOPSIS
    自動化安裝開發工具套件
.DESCRIPTION
    自動安裝 Chocolatey, Git, Node.js, PM2 和 Uptime Kuma 監控工具
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

#region Function: Check and Elevate Admin Privileges
Function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

If (-not (Test-Admin)) {
    Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
    $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
    Start-Process powershell -Verb runAs -ArgumentList $arguments
    Exit
}

Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
#endregion

# 刷新環境變數
function Update-EnvironmentVariables {
    Write-Info "刷新環境變數..."
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
}

# 檢查命令是否存在
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 主要安裝函數
function Install-Chocolatey {
    Write-Info "正在安裝 Chocolatey..."
    if (Test-Command "choco") {
        Write-Success "Chocolatey 已安裝"
        return
    }
    
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Update-EnvironmentVariables
        Write-Success "Chocolatey 安裝完成"
    }
    catch {
        Write-Error "Chocolatey 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-Git {
    Write-Info "正在安裝 Git..."
    if (Test-Command "git") {
        Write-Success "Git 已安裝"
        return
    }
    
    try {
        choco install git -y
        Update-EnvironmentVariables
        Write-Success "Git 安裝完成"
    }
    catch {
        Write-Error "Git 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-NodeJS {
    Write-Info "正在安裝 Node.js..."
    if (Test-Command "node") {
        Write-Success "Node.js 已安裝"
        return
    }
    
    try {
        choco install nodejs -y
        Update-EnvironmentVariables
        Write-Success "Node.js 安裝完成"
    }
    catch {
        Write-Error "Node.js 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2 {
    Write-Info "正在安裝 PM2..."
    if (Test-Command "pm2") {
        Write-Success "PM2 已安裝"
        return
    }
    
    try {
        npm install -g pm2
        Update-EnvironmentVariables
        Write-Success "PM2 安裝完成"
    }
    catch {
        Write-Error "PM2 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-UptimeKuma {
    Write-Info "正在安裝 Uptime Kuma..."
    $installPath = "C:\uptime-kuma"
    
    if (Test-Path $installPath) {
        Write-Warning "Uptime Kuma 目錄已存在，跳過安裝"
        return $installPath
    }
    
    try {
        # 創建安裝目錄
        New-Item -ItemType Directory -Path $installPath -Force | Out-Null
        Set-Location $installPath
        
        # 下載並安裝特定版本
        git clone https://github.com/louislam/uptime-kuma.git .
        git checkout 2.0.0-beta.2
        
        # 安裝依賴
        npm ci --production
        npm run download-dist
        
        Write-Success "Uptime Kuma 安裝完成"
        return $installPath
    }
    catch {
        Write-Error "Uptime Kuma 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function New-PM2EcosystemFile {
    param([string]$UptimeKumaPath)
    
    Write-Info "創建 PM2 生態系統配置文件..."
    
    $ecosystemConfig = @"
{
  "apps": [
    {
      "name": "uptime-kuma",
      "script": "server/server.js",
      "cwd": "$UptimeKumaPath",
      "env": {
        "NODE_ENV": "production",
        "UPTIME_KUMA_PORT": "3001"
      },
      "instances": 1,
      "exec_mode": "fork",
      "watch": false,
      "max_memory_restart": "1G",
      "error_file": "$UptimeKumaPath\\logs\\error.log",
      "out_file": "$UptimeKumaPath\\logs\\out.log",
      "log_file": "$UptimeKumaPath\\logs\\combined.log",
      "time": true
    }
  ]
}
"@
    
    try {
        # 創建日誌目錄
        $logsPath = Join-Path $UptimeKumaPath "logs"
        New-Item -ItemType Directory -Path $logsPath -Force | Out-Null
        
        # 寫入配置文件
        $configPath = Join-Path $UptimeKumaPath "ecosystem.config.json"
        $ecosystemConfig | Out-File -FilePath $configPath -Encoding UTF8
        
        Write-Success "PM2 生態系統配置文件已創建"
        return $configPath
    }
    catch {
        Write-Error "創建 PM2 配置文件失敗: $($_.Exception.Message)"
        throw
    }
}

function Start-UptimeKumaService {
    param([string]$ConfigPath)
    
    Write-Info "啟動 Uptime Kuma 服務..."
    
    try {
        # 停止現有的 uptime-kuma 進程（如果存在）
        pm2 delete uptime-kuma 2>$null
        
        # 啟動服務
        pm2 start $ConfigPath
        pm2 save
        
        Write-Success "Uptime Kuma 服務已啟動"
    }
    catch {
        Write-Error "啟動 Uptime Kuma 服務失敗: $($_.Exception.Message)"
        throw
    }
}

function Show-InstallationSummary {
    Write-ColorOutput "`n" + "="*60 "Magenta"
    Write-ColorOutput "           安裝完成摘要" "Magenta"
    Write-ColorOutput "="*60 "Magenta"
    
    Write-Success "所有工具已成功安裝："
    Write-ColorOutput "  • Chocolatey - Windows 套件管理器" "White"
    Write-ColorOutput "  • Git - 版本控制系統" "White"
    Write-ColorOutput "  • Node.js - JavaScript 運行環境" "White"
    Write-ColorOutput "  • PM2 - 進程管理器" "White"
    Write-ColorOutput "  • Uptime Kuma 2.0.0-beta.2 - 監控工具" "White"
    
    Write-ColorOutput "`n常用 PM2 命令：" "Yellow"
    Write-ColorOutput "  pm2 list                    # 查看所有進程" "Gray"
    Write-ColorOutput "  pm2 logs uptime-kuma        # 查看日誌" "Gray"
    Write-ColorOutput "  pm2 restart uptime-kuma     # 重啟服務" "Gray"
    Write-ColorOutput "  pm2 stop uptime-kuma        # 停止服務" "Gray"
    Write-ColorOutput "  pm2 start uptime-kuma       # 啟動服務" "Gray"
    Write-ColorOutput "  pm2 monit                   # 監控面板" "Gray"
    
    Write-ColorOutput "`nUptime Kuma 訪問信息：" "Yellow"
    Write-ColorOutput "  URL: http://localhost:3001" "Green"
    Write-ColorOutput "  安裝路徑: C:\uptime-kuma" "Gray"
    Write-ColorOutput "  日誌路徑: C:\uptime-kuma\logs" "Gray"
    
    Write-ColorOutput "`n正在開啟瀏覽器..." "Cyan"
}

# 主程序
function Main {
    try {
        # 設定執行策略
        Set-ExecutionPolicy Bypass -Scope Process -Force
        Write-Success "執行策略已設定"
        
        Write-ColorOutput "`n開始自動化安裝程序..." "Magenta"
        Write-ColorOutput "="*50 "Magenta"
        
        # 按順序安裝
        Install-Chocolatey
        Install-Git
        Install-NodeJS
        Install-PM2
        $uptimeKumaPath = Install-UptimeKuma
        
        # 配置和啟動服務
        $configPath = New-PM2EcosystemFile -UptimeKumaPath $uptimeKumaPath
        Start-UptimeKumaService -ConfigPath $configPath
        
        # 等待服務啟動
        Write-Info "等待服務啟動..."
        Start-Sleep -Seconds 5
        
        # 顯示摘要
        Show-InstallationSummary
        
        # 開啟瀏覽器
        Start-Process "http://localhost:3001"
        
        Write-ColorOutput "`n按任意鍵退出..." "Yellow"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
    }
    catch {
        Write-Error "安裝過程中發生錯誤: $($_.Exception.Message)"
        Write-ColorOutput "`n按任意鍵退出..." "Red"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        Read-Host -Prompt "安裝過程中發生錯誤: $($_.Exception.Message)"
        exit 1
    }
}

# 執行主程序
Main