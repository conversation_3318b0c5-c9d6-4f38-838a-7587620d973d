# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }

        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green

        # 測試腳本
        Write-Host "=== 權限提升測試 ===" -ForegroundColor Magenta
        Write-Host "當前用戶: $env:USERNAME" -ForegroundColor Cyan
        Write-Host "執行策略: $(Get-ExecutionPolicy)" -ForegroundColor Cyan
        Write-Host "管理員權限: $(Test-Admin)" -ForegroundColor Green

        Write-Host "`n測試完成！權限提升功能正常運作。" -ForegroundColor Green
        Write-Host "按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Host "測試過程中發生錯誤: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
