#region Function: Check and Elevate Admin Privileges
Function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

If (-not (Test-Admin)) {
    Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
    $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
    Start-Process powershell -Verb runAs -ArgumentList $arguments
    Exit
}

Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
#endregion

# 測試腳本
Write-Host "=== 權限提升測試 ===" -ForegroundColor Magenta
Write-Host "當前用戶: $env:USERNAME" -ForegroundColor Cyan
Write-Host "執行策略: $(Get-ExecutionPolicy)" -ForegroundColor Cyan
Write-Host "管理員權限: $(Test-Admin)" -ForegroundColor Green

Write-Host "`n測試完成！權限提升功能正常運作。" -ForegroundColor Green
Write-Host "按任意鍵退出..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
