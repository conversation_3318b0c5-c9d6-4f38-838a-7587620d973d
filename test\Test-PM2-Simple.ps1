# 測試簡化的 PM2 啟動方式

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# 測試 PM2 命令
function Test-PM2Commands {
    Write-Info "測試 PM2 基本命令..."
    
    try {
        # 檢查 PM2 是否安裝
        $pm2Version = pm2 --version
        Write-Success "PM2 版本: $pm2Version"
        
        # 檢查 PM2 進程列表
        Write-Info "當前 PM2 進程列表："
        pm2 list
        
        # 測試官方推薦的啟動命令格式（不實際啟動）
        Write-Info "測試啟動命令格式..."
        Write-ColorOutput "  命令: pm2 start server/server.js --name uptime-kuma" "Gray"
        Write-Success "命令格式正確"
        
        # 檢查 PM2 日誌輪轉插件
        Write-Info "檢查 PM2 插件..."
        pm2 list | Out-Null
        Write-Success "PM2 功能正常"
        
    }
    catch {
        Write-Error "PM2 測試失敗: $($_.Exception.Message)"
        return $false
    }
    
    return $true
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== PM2 簡化啟動方式測試 ===" "Magenta"
        
        $result = Test-PM2Commands
        
        if ($result) {
            Write-Success "所有測試通過！PM2 簡化啟動方式可以正常使用。"
        } else {
            Write-Error "測試失敗，請檢查 PM2 安裝。"
        }
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "測試過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
