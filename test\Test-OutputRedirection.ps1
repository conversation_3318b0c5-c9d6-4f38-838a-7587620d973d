# 測試輸出重定向修復

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# 測試輸出重定向
function Test-OutputRedirection {
    Write-Info "測試 PowerShell 輸出重定向..."
    
    try {
        # 測試 1: npm 命令輸出重定向
        Write-Info "測試 1: npm 版本檢查（靜默模式）"
        $npmVersion = npm --version *>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "npm 命令輸出重定向正常"
        } else {
            Write-Error "npm 命令執行失敗"
        }
        
        # 測試 2: git 命令輸出重定向
        Write-Info "測試 2: git 版本檢查（靜默模式）"
        git --version *>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "git 命令輸出重定向正常"
        } else {
            Write-Error "git 命令執行失敗"
        }
        
        # 測試 3: PM2 命令輸出重定向
        Write-Info "測試 3: PM2 列表檢查（靜默模式）"
        pm2 list *>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "PM2 命令輸出重定向正常"
        } else {
            Write-Error "PM2 命令執行失敗"
        }
        
        # 測試 4: 模擬長輸出的命令
        Write-Info "測試 4: 模擬長輸出命令"
        $testOutput = "added 692 packages, and audited 693 packages in 2m`n103 packages are looking for funding`nrun npm fund for details`n8 vulnerabilities (3 low, 5 high)"
        Write-Host $testOutput *>$null
        Write-Success "長輸出重定向測試通過"
        
        return $true
    }
    catch {
        Write-Error "輸出重定向測試失敗: $($_.Exception.Message)"
        return $false
    }
}

# 測試錯誤處理
function Test-ErrorHandling {
    Write-Info "測試錯誤處理機制..."
    
    try {
        # 測試不存在的命令
        Write-Info "測試不存在的命令處理"
        try {
            nonexistentcommand *>$null
        }
        catch {
            Write-Success "不存在命令的錯誤處理正常"
        }
        
        # 測試 PM2 刪除不存在的進程
        Write-Info "測試 PM2 刪除不存在進程的處理"
        try {
            pm2 delete nonexistent-process *>$null
        }
        catch {
            Write-Success "PM2 刪除不存在進程的錯誤處理正常"
        }
        
        return $true
    }
    catch {
        Write-Error "錯誤處理測試失敗: $($_.Exception.Message)"
        return $false
    }
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== PowerShell 輸出重定向測試 ===" "Magenta"
        
        $redirectionTest = Test-OutputRedirection
        $errorHandlingTest = Test-ErrorHandling
        
        Write-ColorOutput "`n=== 測試結果 ===" "Magenta"
        
        if ($redirectionTest -and $errorHandlingTest) {
            Write-Success "所有測試通過！輸出重定向修復成功。"
            Write-Info "腳本現在應該不會再出現磁碟機不存在的錯誤。"
        } else {
            Write-Error "部分測試失敗，請檢查相關工具的安裝。"
        }
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "測試過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
