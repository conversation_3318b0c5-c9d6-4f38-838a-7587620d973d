# 測試 Write-ColorOutput 函數的修復

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 測試各種輸出格式
Write-Host "=== 測試 Write-ColorOutput 函數 ===" -ForegroundColor Cyan

Write-Host "`n測試 1: 基本字符串輸出" -ForegroundColor Yellow
Write-ColorOutput "這是一個測試訊息" "Green"

Write-Host "`n測試 2: 重複字符（修復前會出錯的格式）" -ForegroundColor Yellow
Write-ColorOutput ("="*50) "Magenta"

Write-Host "`n測試 3: 字符串連接" -ForegroundColor Yellow
Write-ColorOutput ("`n" + "="*60) "Magenta"
Write-ColorOutput "           測試完成摘要" "Magenta"
Write-ColorOutput ("="*60) "Magenta"

Write-Host "`n測試 4: 各種顏色" -ForegroundColor Yellow
$colors = @("Red", "Green", "Blue", "Yellow", "Cyan", "Magenta", "White")
foreach ($color in $colors) {
    Write-ColorOutput "這是 $color 顏色的文字" $color
}

Write-Host "`n✅ 所有測試通過！Write-ColorOutput 函數工作正常。" -ForegroundColor Green
Write-Host "按任意鍵退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
