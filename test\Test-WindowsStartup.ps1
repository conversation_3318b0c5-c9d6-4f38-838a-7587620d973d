# 測試 Windows 系統啟動項功能

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

# 測試啟動腳本存在性
function Test-StartupScriptExists {
    Write-Info "檢查啟動腳本是否存在..."
    
    $startupScript = Join-Path $PSScriptRoot "..\Start-UptimeKuma-Service.ps1"
    if (Test-Path $startupScript) {
        Write-Success "啟動腳本存在: $startupScript"
        return $true
    } else {
        Write-Error "啟動腳本不存在: $startupScript"
        return $false
    }
}

# 測試管理腳本存在性
function Test-ManageScriptExists {
    Write-Info "檢查管理腳本是否存在..."
    
    $manageScript = Join-Path $PSScriptRoot "..\Manage-WindowsStartup.ps1"
    if (Test-Path $manageScript) {
        Write-Success "管理腳本存在: $manageScript"
        return $true
    } else {
        Write-Error "管理腳本不存在: $manageScript"
        return $false
    }
}

# 測試任務排程器功能
function Test-TaskScheduler {
    Write-Info "測試任務排程器功能..."
    
    try {
        # 嘗試獲取任務列表
        $tasks = Get-ScheduledTask -ErrorAction Stop | Select-Object -First 1
        Write-Success "任務排程器功能正常"
        return $true
    }
    catch {
        Write-Error "任務排程器功能異常: $($_.Exception.Message)"
        return $false
    }
}

# 測試啟動項安裝和移除
function Test-StartupInstallRemove {
    Write-Info "測試啟動項安裝和移除功能..."
    
    $TaskName = "UptimeKuma-AutoStart-Test"
    
    try {
        # 清理可能存在的測試任務
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
        }
        
        # 測試安裝
        Write-Info "測試安裝啟動項..."
        $startupScript = Join-Path $PSScriptRoot "..\Start-UptimeKuma-Service.ps1"
        
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -File `"$startupScript`""
        $trigger = New-ScheduledTaskTrigger -AtStartup
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd
        
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "測試任務" | Out-Null
        
        # 檢查是否安裝成功
        $installedTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($installedTask) {
            Write-Success "啟動項安裝測試成功"
            
            # 測試移除
            Write-Info "測試移除啟動項..."
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            
            $removedTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
            if (-not $removedTask) {
                Write-Success "啟動項移除測試成功"
                return $true
            } else {
                Write-Error "啟動項移除測試失敗"
                return $false
            }
        } else {
            Write-Error "啟動項安裝測試失敗"
            return $false
        }
    }
    catch {
        Write-Error "啟動項安裝/移除測試失敗: $($_.Exception.Message)"
        
        # 清理測試任務
        try {
            $testTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
            if ($testTask) {
                Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            }
        }
        catch {
            # 忽略清理錯誤
        }
        
        return $false
    }
}

# 測試 Uptime Kuma 安裝
function Test-UptimeKumaInstallation {
    Write-Info "檢查 Uptime Kuma 安裝狀態..."
    
    $uptimeKumaPath = "C:\uptime-kuma"
    if (Test-Path $uptimeKumaPath) {
        Write-Success "Uptime Kuma 安裝目錄存在: $uptimeKumaPath"
        
        # 檢查關鍵文件
        $serverScript = Join-Path $uptimeKumaPath "server\server.js"
        if (Test-Path $serverScript) {
            Write-Success "Uptime Kuma 服務腳本存在"
            return $true
        } else {
            Write-Warning "Uptime Kuma 服務腳本不存在"
            return $false
        }
    } else {
        Write-Warning "Uptime Kuma 未安裝"
        return $false
    }
}

# 測試 PM2 安裝
function Test-PM2Installation {
    Write-Info "檢查 PM2 安裝狀態..."
    
    try {
        $pm2Version = pm2 --version 2>$null
        if ($pm2Version) {
            Write-Success "PM2 已安裝，版本: $pm2Version"
            return $true
        } else {
            Write-Warning "PM2 未安裝或不可用"
            return $false
        }
    }
    catch {
        Write-Warning "PM2 檢查失敗: $($_.Exception.Message)"
        return $false
    }
}

# 顯示測試結果摘要
function Show-TestSummary {
    param(
        [bool]$ScriptExists,
        [bool]$ManageExists,
        [bool]$TaskScheduler,
        [bool]$InstallRemove,
        [bool]$UptimeKuma,
        [bool]$PM2
    )
    
    Write-ColorOutput "`n" + "="*60 "Magenta"
    Write-ColorOutput "           測試結果摘要" "Magenta"
    Write-ColorOutput "="*60 "Magenta"
    
    $tests = @(
        @{ Name = "啟動腳本存在性"; Result = $ScriptExists },
        @{ Name = "管理腳本存在性"; Result = $ManageExists },
        @{ Name = "任務排程器功能"; Result = $TaskScheduler },
        @{ Name = "啟動項安裝/移除"; Result = $InstallRemove },
        @{ Name = "Uptime Kuma 安裝"; Result = $UptimeKuma },
        @{ Name = "PM2 安裝"; Result = $PM2 }
    )
    
    $passedTests = 0
    $totalTests = $tests.Count
    
    foreach ($test in $tests) {
        if ($test.Result) {
            Write-Success "$($test.Name)"
            $passedTests++
        } else {
            Write-Error "$($test.Name)"
        }
    }
    
    Write-ColorOutput "`n測試結果: $passedTests/$totalTests 通過" "Yellow"
    
    if ($passedTests -eq $totalTests) {
        Write-Success "所有測試通過！Windows 啟動項功能完全正常。"
    } elseif ($passedTests -ge 4) {
        Write-Warning "大部分測試通過，啟動項功能基本可用。"
    } else {
        Write-Error "多項測試失敗，請檢查安裝和配置。"
    }
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== Windows 系統啟動項功能測試 ===" "Magenta"
        
        # 執行各項測試
        $scriptExists = Test-StartupScriptExists
        $manageExists = Test-ManageScriptExists
        $taskScheduler = Test-TaskScheduler
        $installRemove = Test-StartupInstallRemove
        $uptimeKuma = Test-UptimeKumaInstallation
        $pm2 = Test-PM2Installation
        
        # 顯示測試摘要
        Show-TestSummary -ScriptExists $scriptExists -ManageExists $manageExists -TaskScheduler $taskScheduler -InstallRemove $installRemove -UptimeKuma $uptimeKuma -PM2 $pm2
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "測試過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
