# 測試 Windows 系統啟動配置

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

# 測試 pm2-installer 安裝
function Test-PM2Installer {
    Write-Info "檢查 pm2-installer 安裝狀態..."
    
    try {
        # 檢查是否已安裝 pm2-installer
        $installerCheck = npm list -g pm2-installer 2>$null
        if ($installerCheck -and $installerCheck -match "pm2-installer@") {
            Write-Success "pm2-installer 已安裝"
            
            # 獲取版本信息
            $versionMatch = $installerCheck | Select-String "pm2-installer@([\d\.]+)"
            if ($versionMatch) {
                $version = $versionMatch.Matches[0].Groups[1].Value
                Write-Info "版本: $version"
            }
            return $true
        } else {
            Write-Error "pm2-installer 未安裝"
            return $false
        }
    }
    catch {
        Write-Error "檢查 pm2-installer 失敗: $($_.Exception.Message)"
        return $false
    }
}

# 測試 Windows 服務狀態
function Test-WindowsService {
    Write-Info "檢查 PM2 Windows 服務狀態..."
    
    try {
        $pm2Service = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        
        if ($pm2Service) {
            Write-Success "PM2 Windows 服務已安裝"
            Write-ColorOutput "  服務名稱: $($pm2Service.Name)" "Gray"
            Write-ColorOutput "  顯示名稱: $($pm2Service.DisplayName)" "Gray"
            Write-ColorOutput "  運行狀態: $($pm2Service.Status)" "White"
            Write-ColorOutput "  啟動類型: $($pm2Service.StartType)" "White"
            
            # 檢查服務狀態
            if ($pm2Service.Status -eq "Running") {
                Write-Success "PM2 服務正在運行中"
            } else {
                Write-Warning "PM2 服務未運行"
            }
            
            # 檢查啟動類型
            if ($pm2Service.StartType -eq "Automatic") {
                Write-Success "PM2 服務已設定為自動啟動"
            } else {
                Write-Warning "PM2 服務未設定為自動啟動"
            }
            
            return $true
        } else {
            Write-Error "PM2 Windows 服務未安裝"
            return $false
        }
    }
    catch {
        Write-Error "檢查 Windows 服務失敗: $($_.Exception.Message)"
        return $false
    }
}

# 測試 PM2 進程狀態
function Test-PM2Processes {
    Write-Info "檢查 PM2 進程狀態..."
    
    try {
        # 檢查 PM2 是否可用
        $pm2Version = pm2 --version 2>$null
        if (-not $pm2Version) {
            Write-Error "PM2 命令不可用"
            return $false
        }
        
        Write-Success "PM2 版本: $pm2Version"
        
        # 檢查 PM2 進程列表
        Write-Info "PM2 進程列表："
        pm2 list
        
        # 檢查 uptime-kuma 進程
        $uptimeKumaProcess = pm2 list | Select-String "uptime-kuma"
        if ($uptimeKumaProcess) {
            Write-Success "找到 Uptime Kuma 進程"
            
            # 檢查進程狀態
            $onlineStatus = pm2 list | Select-String "uptime-kuma.*online"
            if ($onlineStatus) {
                Write-Success "Uptime Kuma 進程狀態: online"
                return $true
            } else {
                Write-Warning "Uptime Kuma 進程存在但可能不在線"
                return $false
            }
        } else {
            Write-Error "未找到 Uptime Kuma 進程"
            return $false
        }
    }
    catch {
        Write-Error "檢查 PM2 進程失敗: $($_.Exception.Message)"
        return $false
    }
}

# 測試 HTTP 連接
function Test-HTTPConnection {
    Write-Info "測試 Uptime Kuma HTTP 連接..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 10 -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            Write-Success "HTTP 連接成功 (狀態碼: $($response.StatusCode))"
            return $true
        } else {
            Write-Warning "HTTP 連接異常 (狀態碼: $($response.StatusCode))"
            return $false
        }
    }
    catch {
        Write-Error "HTTP 連接失敗: $($_.Exception.Message)"
        return $false
    }
}

# 生成測試報告
function Generate-TestReport {
    param(
        [bool]$PM2InstallerTest,
        [bool]$WindowsServiceTest,
        [bool]$PM2ProcessTest,
        [bool]$HTTPTest
    )
    
    Write-ColorOutput "`n" + "="*60 "Magenta"
    Write-ColorOutput "           Windows 啟動配置測試報告" "Magenta"
    Write-ColorOutput "="*60 "Magenta"
    
    $testResults = @(
        @{ Name = "pm2-installer 安裝"; Result = $PM2InstallerTest },
        @{ Name = "Windows 服務配置"; Result = $WindowsServiceTest },
        @{ Name = "PM2 進程狀態"; Result = $PM2ProcessTest },
        @{ Name = "HTTP 服務連接"; Result = $HTTPTest }
    )
    
    $passedTests = 0
    $totalTests = $testResults.Count
    
    foreach ($test in $testResults) {
        $status = if ($test.Result) { "✓ 通過" } else { "✗ 失敗" }
        $color = if ($test.Result) { "Green" } else { "Red" }
        
        Write-ColorOutput "  $($test.Name): $status" $color
        
        if ($test.Result) {
            $passedTests++
        }
    }
    
    Write-ColorOutput "`n測試結果: $passedTests/$totalTests 項測試通過" "White"
    
    if ($passedTests -eq $totalTests) {
        Write-Success "🎉 所有測試通過！Windows 啟動配置完全正常。"
        Write-Info "Uptime Kuma 將在系統重新啟動後自動運行。"
    } elseif ($passedTests -ge 2) {
        Write-Warning "⚠️ 部分測試通過，基本功能可能正常。"
        Write-Info "建議檢查失敗的項目並進行修復。"
    } else {
        Write-Error "❌ 多項測試失敗，Windows 啟動配置可能有問題。"
        Write-Info "建議重新執行配置或檢查安裝。"
    }
}

# 主測試函數
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== Windows 系統啟動配置測試 ===" "Magenta"
        Write-ColorOutput "此測試將檢查 PM2 和 Uptime Kuma 的 Windows 啟動配置" "Gray"
        
        # 執行各項測試
        $pm2InstallerResult = Test-PM2Installer
        Write-ColorOutput "" "White"
        
        $windowsServiceResult = Test-WindowsService
        Write-ColorOutput "" "White"
        
        $pm2ProcessResult = Test-PM2Processes
        Write-ColorOutput "" "White"
        
        $httpResult = Test-HTTPConnection
        Write-ColorOutput "" "White"
        
        # 生成測試報告
        Generate-TestReport -PM2InstallerTest $pm2InstallerResult -WindowsServiceTest $windowsServiceResult -PM2ProcessTest $pm2ProcessResult -HTTPTest $httpResult
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        Write-Error "測試過程中發生錯誤: $($_.Exception.Message)"
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

# 執行主程序
Main
