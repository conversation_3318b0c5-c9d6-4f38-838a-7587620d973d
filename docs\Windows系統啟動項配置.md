# Windows 系統啟動項配置說明

## 🎯 功能概述

為了解決 Windows 系統中 `pm2 startup` 命令不支援的問題，我們實現了基於 Windows 任務排程器的自動啟動解決方案，確保 Uptime Kuma 服務在系統重新啟動後自動運行。

## 🏗️ 實現方案

### 選擇任務排程器的原因

我們評估了多種 Windows 啟動方案：

| 方案 | 優點 | 缺點 | 選擇 |
|------|------|------|------|
| Windows 服務 | 最穩定，系統級別 | 複雜，需要額外依賴 | ❌ |
| **任務排程器** | **內建功能，穩定，易管理** | **需要正確配置** | ✅ |
| 註冊表啟動項 | 簡單 | 用戶級別，不夠穩定 | ❌ |
| 啟動資料夾 | 簡單 | 依賴用戶登入 | ❌ |

## 📁 文件結構

```
Install uptime kuma/
├── Install-DevTools.ps1           # 主安裝腳本（已修改）
├── Start-UptimeKuma-Service.ps1   # 系統啟動時執行的腳本
├── Manage-WindowsStartup.ps1      # 啟動項管理工具
└── docs/
    └── Windows系統啟動項配置.md   # 本說明文件
```

## 🔧 核心組件

### 1. Start-UptimeKuma-Service.ps1
**系統啟動時執行的主腳本**

功能：
- ✅ 等待系統完全啟動和網路連接
- ✅ 設定必要的環境變數
- ✅ 啟動 PM2 和 Uptime Kuma 服務
- ✅ 詳細的日誌記錄
- ✅ 錯誤處理和恢復機制

關鍵特性：
```powershell
# 忽略 Node.js 棄用警告
$env:NODE_NO_WARNINGS = "1"

# 等待網路連接
Wait-NetworkConnection

# 恢復 PM2 進程
pm2 resurrect

# 日誌記錄到 C:\uptime-kuma\logs\startup.log
```

### 2. Manage-WindowsStartup.ps1
**啟動項管理工具**

支援的操作：
- `install` - 安裝系統啟動項
- `remove` - 移除系統啟動項
- `status` - 顯示啟動項狀態
- `test` - 測試啟動腳本
- `help` - 顯示幫助信息

### 3. Install-WindowsStartup 函數
**集成到主安裝腳本中的啟動項安裝功能**

## ⚙️ 任務排程器配置

### 任務設定
- **任務名稱**: `UptimeKuma-AutoStart`
- **觸發器**: 系統啟動時
- **執行身份**: SYSTEM（系統帳戶）
- **權限級別**: 最高權限
- **執行方式**: 隱藏視窗執行

### PowerShell 命令
```powershell
PowerShell.exe -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -File "C:\path\to\Start-UptimeKuma-Service.ps1"
```

### 任務設置
- ✅ 允許在電池供電時啟動
- ✅ 不在電池供電時停止
- ✅ 可用時啟動
- ✅ 不在閒置時停止

## 🚀 使用方法

### 自動安裝（推薦）
運行主安裝腳本時會自動詢問：
```powershell
.\Install-DevTools.ps1
```

### 手動管理
```powershell
# 安裝啟動項
.\Manage-WindowsStartup.ps1 install

# 移除啟動項
.\Manage-WindowsStartup.ps1 remove

# 檢查狀態
.\Manage-WindowsStartup.ps1 status

# 測試腳本
.\Manage-WindowsStartup.ps1 test
```

## 📋 啟動流程

1. **系統啟動** → Windows 任務排程器觸發
2. **等待系統就緒** → 等待10秒確保系統完全啟動
3. **網路連接檢查** → 等待網路連接建立（最多60秒）
4. **環境變數設定** → 設定 Node.js 和 PATH 環境變數
5. **PM2 服務恢復** → 執行 `pm2 resurrect` 恢復保存的進程
6. **服務驗證** → 檢查 Uptime Kuma 是否正常運行
7. **日誌記錄** → 記錄啟動過程到日誌文件

## 📝 日誌管理

### 日誌位置
```
C:\uptime-kuma\logs\startup.log
```

### 日誌格式
```
[2024-01-20 08:30:15] [INFO] === Uptime Kuma 系統啟動腳本開始執行 ===
[2024-01-20 08:30:25] [INFO] 等待網路連接...
[2024-01-20 08:30:30] [INFO] 網路連接已建立
[2024-01-20 08:30:31] [INFO] 設定環境變數...
[2024-01-20 08:30:32] [INFO] PM2 版本: 5.3.0
[2024-01-20 08:30:35] [INFO] Uptime Kuma 服務已成功啟動
```

## 🔍 故障排除

### 常見問題

1. **服務未啟動**
   - 檢查任務排程器中的任務狀態
   - 查看啟動日誌：`C:\uptime-kuma\logs\startup.log`
   - 確認 PM2 和 Node.js 已正確安裝

2. **權限問題**
   - 確保任務以 SYSTEM 身份運行
   - 檢查腳本執行策略設定

3. **環境變數問題**
   - 檢查 PATH 是否包含 Node.js 和 npm 路徑
   - 確認 NODE_NO_WARNINGS 設定

### 手動測試
```powershell
# 測試啟動腳本
.\Start-UptimeKuma-Service.ps1

# 檢查 PM2 狀態
pm2 list

# 檢查任務排程器
Get-ScheduledTask -TaskName "UptimeKuma-AutoStart"
```

## 🎉 優勢總結

- ✅ **穩定可靠**: 使用 Windows 內建的任務排程器
- ✅ **系統級別**: 不依賴用戶登入，系統啟動即運行
- ✅ **易於管理**: 提供完整的管理工具和狀態檢查
- ✅ **詳細日誌**: 完整的啟動過程記錄
- ✅ **錯誤恢復**: 智能的錯誤處理和服務恢復機制
- ✅ **用戶友好**: 簡單的安裝和配置過程

這個解決方案完美替代了 Linux/macOS 中的 `pm2 startup` 功能，為 Windows 用戶提供了同樣穩定可靠的自動啟動體驗。
